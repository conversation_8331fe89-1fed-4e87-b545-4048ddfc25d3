"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./app/dashboard/students/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/students/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/bulk-delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/bulk-delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/add-student-modal */ \"(app-pages-browser)/./components/modals/add-student-modal.tsx\");\n/* harmony import */ var _components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/student-document-modal */ \"(app-pages-browser)/./components/modals/student-document-modal.tsx\");\n/* harmony import */ var _components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/print-student-modal */ \"(app-pages-browser)/./components/modals/print-student-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// import { EditStudentModal } from \"@/components/modals/edit-student-modal\" // Using unified AddStudentModal instead\n\n\n\n\n\n\n\n\nfunction StudentsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '';\n        try {\n            var _response_data, _response_data1;\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.getAll({\n                page,\n                limit,\n                search,\n                sort_by: 'first_name',\n                sort_order: 'ASC'\n            });\n            setStudents(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.students) || []);\n            if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.pagination) {\n                setPagination(response.data.pagination);\n            }\n        } catch (error) {\n            console.error('Error fetching students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch students. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentsPage.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentsPage.useEffect\"], []);\n    const handleSaveStudent = async (updatedStudent)=>{\n        try {\n            console.log('handleSaveStudent called with:', updatedStudent);\n            // Check if data is already in camelCase (from modal) or snake_case (from other sources)\n            const isFromModal = updatedStudent.firstName !== undefined;\n            let updateData;\n            let studentId;\n            if (isFromModal) {\n                // Data from modal is already in camelCase format\n                updateData = {\n                    firstName: updatedStudent.firstName,\n                    lastName: updatedStudent.lastName,\n                    middleName: updatedStudent.middleName,\n                    email: updatedStudent.email,\n                    phone: updatedStudent.phone,\n                    dateOfBirth: updatedStudent.dateOfBirth,\n                    gender: updatedStudent.gender,\n                    address: updatedStudent.address,\n                    bloodGroup: updatedStudent.bloodGroup,\n                    nationality: updatedStudent.nationality,\n                    religion: updatedStudent.religion,\n                    medicalConditions: updatedStudent.medicalConditions,\n                    emergencyContactName: updatedStudent.emergencyContactName,\n                    emergencyContactPhone: updatedStudent.emergencyContactPhone,\n                    emergencyContactRelationship: updatedStudent.emergencyContactRelationship,\n                    currentClassId: updatedStudent.currentClassId,\n                    status: updatedStudent.status\n                };\n                studentId = updatedStudent.id;\n            } else {\n                // Data from other sources - convert snake_case to camelCase\n                updateData = {\n                    firstName: updatedStudent.first_name,\n                    lastName: updatedStudent.last_name,\n                    middleName: updatedStudent.middle_name,\n                    email: updatedStudent.email,\n                    phone: updatedStudent.phone,\n                    dateOfBirth: updatedStudent.date_of_birth,\n                    gender: updatedStudent.gender,\n                    address: updatedStudent.address,\n                    bloodGroup: updatedStudent.blood_group,\n                    nationality: updatedStudent.nationality,\n                    religion: updatedStudent.religion,\n                    medicalConditions: updatedStudent.medical_conditions,\n                    emergencyContactName: updatedStudent.emergency_contact_name,\n                    emergencyContactPhone: updatedStudent.emergency_contact_phone,\n                    emergencyContactRelationship: updatedStudent.emergency_contact_relation,\n                    currentClassId: updatedStudent.current_class_id || updatedStudent.class_id,\n                    status: updatedStudent.status\n                };\n                studentId = updatedStudent.id;\n            }\n            await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.update(String(studentId), updateData);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            const firstName = isFromModal ? updatedStudent.firstName : updatedStudent.first_name;\n            const lastName = isFromModal ? updatedStudent.lastName : updatedStudent.last_name;\n            toast({\n                title: \"Student Updated\",\n                description: \"\".concat(firstName, \" \").concat(lastName, \"'s information has been updated successfully.\")\n            });\n        } catch (error) {\n            console.error('Error updating student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteStudent = async (id)=>{\n        try {\n            const stringId = String(id);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.delete(stringId);\n            setStudents((prev)=>prev.filter((student)=>String(student.id) !== stringId));\n            toast({\n                title: \"Student Deleted\",\n                description: \"Student has been deleted successfully.\"\n            });\n        } catch (error) {\n            console.error('Error deleting student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddStudent = async (newStudent)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.create(newStudent);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Added\",\n                description: \"\".concat(newStudent.firstName, \" \").concat(newStudent.lastName, \" has been added successfully.\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        try {\n            // Delete students one by one (could be optimized with bulk delete API)\n            await Promise.all(selectedRows.map((id)=>_lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.delete(String(id))));\n            setStudents((prev)=>prev.filter((student)=>!selectedRows.includes(String(student.id))));\n            toast({\n                title: \"Students Deleted\",\n                description: \"\".concat(selectedRows.length, \" students have been deleted successfully.\")\n            });\n            setSelectedRows([]);\n        } catch (error) {\n            console.error('Error deleting students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete students. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportStudents = async ()=>{\n        try {\n            if (students.length === 0) {\n                toast({\n                    title: \"No Data\",\n                    description: \"No students to export.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Generate CSV export\n            const csvData = students.map((student)=>({\n                    'Student ID': student.student_id || '',\n                    'Name': \"\".concat(student.first_name, \" \").concat(student.last_name),\n                    'Email': student.email || '',\n                    'Phone': student.phone || '',\n                    'Gender': student.gender || '',\n                    'Date of Birth': student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : '',\n                    'Blood Group': student.blood_group || '',\n                    'Nationality': student.nationality || '',\n                    'Religion': student.religion || '',\n                    'Address': student.address || '',\n                    'Emergency Contact': student.emergency_contact_name || '',\n                    'Emergency Phone': student.emergency_contact_phone || '',\n                    'Class': student.class_name || '',\n                    'Grade Level': student.grade_level || '',\n                    'Academic Year': student.academic_year || '',\n                    'Status': student.user_status || '',\n                    'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '',\n                    'Admission Number': student.admission_number || '',\n                    'Roll Number': student.roll_number || '',\n                    'Medical Conditions': student.medical_conditions || '',\n                    'Transport Required': student.transport_required ? 'Yes' : 'No',\n                    'Hostel Required': student.hostel_required ? 'Yes' : 'No'\n                }));\n            // Helper function to escape CSV values\n            const escapeCSV = (value)=>{\n                if (value === null || value === undefined) return '';\n                const str = String(value);\n                if (str.includes(',') || str.includes('\"') || str.includes('\\n')) {\n                    return '\"'.concat(str.replace(/\"/g, '\"\"'), '\"');\n                }\n                return str;\n            };\n            const csvContent = [\n                Object.keys(csvData[0]).join(','),\n                ...csvData.map((row)=>Object.values(row).map(escapeCSV).join(','))\n            ].join('\\n');\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"students_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            a.click();\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Export Successful\",\n                description: \"Student data has been exported successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to export student data.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImportStudents = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = '.csv';\n        input.onchange = async (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (!file) return;\n            try {\n                const text = await file.text();\n                const lines = text.split('\\n').filter((line)=>line.trim());\n                if (lines.length < 2) {\n                    toast({\n                        title: \"Invalid File\",\n                        description: \"CSV file must have at least a header and one data row.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Parse CSV (basic implementation)\n                const headers = lines[0].split(',').map((h)=>h.trim());\n                const requiredHeaders = [\n                    'firstName',\n                    'lastName',\n                    'email',\n                    'dateOfBirth',\n                    'gender'\n                ];\n                const missingHeaders = requiredHeaders.filter((h)=>!headers.includes(h));\n                if (missingHeaders.length > 0) {\n                    toast({\n                        title: \"Invalid CSV Format\",\n                        description: \"Missing required columns: \".concat(missingHeaders.join(', ')),\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                const students = lines.slice(1).map((line)=>{\n                    const values = line.split(',').map((v)=>v.trim().replace(/^\"|\"$/g, ''));\n                    const student = {};\n                    headers.forEach((header, index)=>{\n                        student[header] = values[index] || '';\n                    });\n                    return student;\n                });\n                // Validate and import students\n                const validStudents = students.filter((student)=>student.firstName && student.lastName && student.email && student.dateOfBirth && student.gender);\n                if (validStudents.length === 0) {\n                    toast({\n                        title: \"No Valid Students\",\n                        description: \"No valid student records found in the CSV file.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Use bulk create API\n                await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.bulkCreate(validStudents);\n                toast({\n                    title: \"Import Successful\",\n                    description: \"Successfully imported \".concat(validStudents.length, \" students.\")\n                });\n                // Refresh the students list\n                await fetchStudents();\n            } catch (error) {\n                console.error('Import error:', error);\n                toast({\n                    title: \"Import Failed\",\n                    description: \"Failed to import students. Please check the file format.\",\n                    variant: \"destructive\"\n                });\n            }\n        };\n        input.click();\n    };\n    const handlePrintAll = ()=>{\n        if (students.length === 0) {\n            toast({\n                title: \"No Data\",\n                description: \"No students to print.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create a new window for printing\n        const printWindow = window.open('', '_blank');\n        if (!printWindow) return;\n        const printContent = '\\n      <!DOCTYPE html>\\n      <html>\\n        <head>\\n          <title>Students List</title>\\n          <style>\\n            body {\\n              font-family: Arial, sans-serif;\\n              margin: 20px;\\n              line-height: 1.4;\\n            }\\n            .header {\\n              text-align: center;\\n              border-bottom: 2px solid #333;\\n              padding-bottom: 20px;\\n              margin-bottom: 30px;\\n            }\\n            table {\\n              width: 100%;\\n              border-collapse: collapse;\\n              margin-top: 20px;\\n            }\\n            th, td {\\n              border: 1px solid #ddd;\\n              padding: 8px;\\n              text-align: left;\\n              font-size: 12px;\\n            }\\n            th {\\n              background-color: #f5f5f5;\\n              font-weight: bold;\\n            }\\n            .footer {\\n              margin-top: 30px;\\n              text-align: center;\\n              font-size: 10px;\\n              color: #666;\\n            }\\n            @media print {\\n              body { margin: 0; }\\n              .no-print { display: none; }\\n            }\\n          </style>\\n        </head>\\n        <body>\\n          <div class=\"header\">\\n            <h1>Students List</h1>\\n            <p>Total Students: '.concat(students.length, \"</p>\\n            <p>Generated on: \").concat(new Date().toLocaleDateString(), \"</p>\\n          </div>\\n\\n          <table>\\n            <thead>\\n              <tr>\\n                <th>Student ID</th>\\n                <th>Name</th>\\n                <th>Email</th>\\n                <th>Phone</th>\\n                <th>Class</th>\\n                <th>Status</th>\\n                <th>Admission Date</th>\\n              </tr>\\n            </thead>\\n            <tbody>\\n              \").concat(students.map((student)=>\"\\n                <tr>\\n                  <td>\".concat(student.student_id || '', \"</td>\\n                  <td>\").concat(student.first_name, \" \").concat(student.last_name, \"</td>\\n                  <td>\").concat(student.email || '', \"</td>\\n                  <td>\").concat(student.phone || '', \"</td>\\n                  <td>\").concat(student.class_name || '', \"</td>\\n                  <td>\").concat(student.user_status || '', \"</td>\\n                  <td>\").concat(student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '', \"</td>\\n                </tr>\\n              \")).join(''), '\\n            </tbody>\\n          </table>\\n\\n          <div class=\"footer\">\\n            <p>School Management System - Students Report</p>\\n          </div>\\n        </body>\\n      </html>\\n    ');\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        printWindow.focus();\n        // Wait for content to load then print\n        setTimeout(()=>{\n            printWindow.print();\n            printWindow.close();\n        }, 250);\n        toast({\n            title: \"Print Started\",\n            description: \"Preparing student list for printing.\"\n        });\n    };\n    const columns = [\n        {\n            id: \"select\",\n            header: (param)=>{\n                let { table } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected() && \"indeterminate\",\n                    onCheckedChange: (value)=>table.toggleAllPageRowsSelected(!!value),\n                    \"aria-label\": \"Select all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 535,\n                    columnNumber: 9\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: row.getIsSelected(),\n                    onCheckedChange: (value)=>row.toggleSelected(!!value),\n                    \"aria-label\": \"Select row\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 542,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false,\n            enableHiding: false\n        },\n        {\n            accessorKey: \"student_id\",\n            header: \"Student ID\"\n        },\n        {\n            accessorKey: \"photo\",\n            header: \"Photo\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                    className: \"h-10 w-10\",\n                    children: student.profile_picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: student.profile_picture || \"/placeholder.svg\",\n                        alt: \"\".concat(student.first_name, \" \").concat(student.last_name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 563,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        children: [\n                            student.first_name.charAt(0),\n                            student.last_name.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 565,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 561,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"first_name\",\n            header: \"Name\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return \"\".concat(student.first_name, \" \").concat(student.last_name);\n            }\n        },\n        {\n            accessorKey: \"class_name\",\n            header: \"Class\"\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Email\",\n            cell: (param)=>{\n                let { row } = param;\n                const email = row.getValue(\"email\");\n                return email || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: \"Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const phone = row.getValue(\"phone\");\n                return phone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"gender\",\n            header: \"Gender\",\n            cell: (param)=>{\n                let { row } = param;\n                const gender = row.getValue(\"gender\");\n                return gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"date_of_birth\",\n            header: \"Date of Birth\",\n            cell: (param)=>{\n                let { row } = param;\n                const dob = row.getValue(\"date_of_birth\");\n                return dob ? new Date(dob).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"admission_date\",\n            header: \"Admission Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const admissionDate = row.getValue(\"admission_date\");\n                return admissionDate ? new Date(admissionDate).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_name\",\n            header: \"Emergency Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const contact = row.getValue(\"emergency_contact_name\");\n                return contact || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_phone\",\n            header: \"Emergency Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const contactPhone = row.getValue(\"emergency_contact_phone\");\n                return contactPhone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"user_status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const status = row.getValue(\"user_status\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: status === \"active\" ? \"default\" : \"outline\",\n                    children: status || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 644,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/students/\".concat(student.id)),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 15\n                                }, this),\n                                \"Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 653,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_12__.StudentDocumentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Docs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_13__.PrintStudentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 671,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__.AddStudentModal, {\n                            student: student,\n                            mode: \"edit\",\n                            onUpdate: handleSaveStudent,\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 683,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 17\n                            }, void 0)\n                        }, \"edit-\".concat(student.id), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 676,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_9__.DeleteConfirmationModal, {\n                            title: \"Delete Student\",\n                            description: \"Are you sure you want to delete \".concat(student.first_name, \" \").concat(student.last_name, \"? This action cannot be undone.\"),\n                            onConfirm: ()=>handleDeleteStudent(student.id),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 687,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 652,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 707,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Loading students...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 708,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm text-muted-foreground\",\n                        children: \"Please wait while we fetch the student data.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 709,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 706,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n            lineNumber: 705,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 718,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleImportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 720,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleExportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 724,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrintAll,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Print All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 728,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__.AddStudentModal, {\n                                onAdd: handleAddStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Add Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 735,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 717,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"View and manage all students in the system\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__.BulkDeleteConfirmationModal, {\n                                    title: \"Delete Selected Students\",\n                                    description: \"Are you sure you want to delete \".concat(selectedRows.length, \" selected students? This action cannot be undone.\"),\n                                    count: selectedRows.length,\n                                    onConfirm: handleBulkDelete\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 750,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 745,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 744,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 762,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-gray-600\",\n                                        children: \"Loading students...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 761,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 760,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                            columns: columns,\n                            data: students,\n                            searchKey: \"first_name\",\n                            searchPlaceholder: \"Search students...\",\n                            onPrint: handlePrintAll,\n                            onExport: handleExportStudents\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 767,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 758,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 743,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n        lineNumber: 716,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentsPage, \"2mPRm9Jx2cynZ5f7wAONxa8oUKE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = StudentsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/students/page.tsx\n"));

/***/ })

});