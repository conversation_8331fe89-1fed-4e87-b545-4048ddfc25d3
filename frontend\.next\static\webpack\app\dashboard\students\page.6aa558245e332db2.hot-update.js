"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./app/dashboard/students/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/students/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/bulk-delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/bulk-delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/add-student-modal */ \"(app-pages-browser)/./components/modals/add-student-modal.tsx\");\n/* harmony import */ var _components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/student-document-modal */ \"(app-pages-browser)/./components/modals/student-document-modal.tsx\");\n/* harmony import */ var _components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/print-student-modal */ \"(app-pages-browser)/./components/modals/print-student-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// import { EditStudentModal } from \"@/components/modals/edit-student-modal\" // Using unified AddStudentModal instead\n\n\n\n\n\n\n\n\nfunction StudentsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '';\n        try {\n            var _response_data, _response_data1;\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.getAll({\n                page,\n                limit,\n                search,\n                sort_by: 'first_name',\n                sort_order: 'ASC'\n            });\n            setStudents(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.students) || []);\n            if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.pagination) {\n                setPagination(response.data.pagination);\n            }\n        } catch (error) {\n            console.error('Error fetching students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch students. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentsPage.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentsPage.useEffect\"], []);\n    const handleSaveStudent = async (updatedStudent)=>{\n        try {\n            // Check if data is already in camelCase (from modal) or snake_case (from other sources)\n            const isFromModal = updatedStudent.firstName !== undefined;\n            let updateData;\n            let studentId;\n            if (isFromModal) {\n                // Data from modal is already in camelCase format\n                updateData = {\n                    firstName: updatedStudent.firstName,\n                    lastName: updatedStudent.lastName,\n                    middleName: updatedStudent.middleName,\n                    email: updatedStudent.email,\n                    phone: updatedStudent.phone,\n                    dateOfBirth: updatedStudent.dateOfBirth,\n                    gender: updatedStudent.gender,\n                    address: updatedStudent.address,\n                    bloodGroup: updatedStudent.bloodGroup,\n                    nationality: updatedStudent.nationality,\n                    religion: updatedStudent.religion,\n                    medicalConditions: updatedStudent.medicalConditions,\n                    emergencyContactName: updatedStudent.emergencyContactName,\n                    emergencyContactPhone: updatedStudent.emergencyContactPhone,\n                    emergencyContactRelationship: updatedStudent.emergencyContactRelationship,\n                    currentClassId: updatedStudent.currentClassId,\n                    status: updatedStudent.status\n                };\n                studentId = updatedStudent.id;\n            } else {\n                // Data from other sources - convert snake_case to camelCase\n                updateData = {\n                    firstName: updatedStudent.first_name,\n                    lastName: updatedStudent.last_name,\n                    middleName: updatedStudent.middle_name,\n                    email: updatedStudent.email,\n                    phone: updatedStudent.phone,\n                    dateOfBirth: updatedStudent.date_of_birth,\n                    gender: updatedStudent.gender,\n                    address: updatedStudent.address,\n                    bloodGroup: updatedStudent.blood_group,\n                    nationality: updatedStudent.nationality,\n                    religion: updatedStudent.religion,\n                    medicalConditions: updatedStudent.medical_conditions,\n                    emergencyContactName: updatedStudent.emergency_contact_name,\n                    emergencyContactPhone: updatedStudent.emergency_contact_phone,\n                    emergencyContactRelationship: updatedStudent.emergency_contact_relation,\n                    currentClassId: updatedStudent.current_class_id || updatedStudent.class_id,\n                    status: updatedStudent.status\n                };\n                studentId = updatedStudent.id;\n            }\n            await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.update(String(studentId), updateData);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Updated\",\n                description: \"\".concat(updatedStudent.first_name, \" \").concat(updatedStudent.last_name, \"'s information has been updated successfully.\")\n            });\n        } catch (error) {\n            console.error('Error updating student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteStudent = async (id)=>{\n        try {\n            const stringId = String(id);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.delete(stringId);\n            setStudents((prev)=>prev.filter((student)=>String(student.id) !== stringId));\n            toast({\n                title: \"Student Deleted\",\n                description: \"Student has been deleted successfully.\"\n            });\n        } catch (error) {\n            console.error('Error deleting student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddStudent = async (newStudent)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.create(newStudent);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Added\",\n                description: \"\".concat(newStudent.firstName, \" \").concat(newStudent.lastName, \" has been added successfully.\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        try {\n            // Delete students one by one (could be optimized with bulk delete API)\n            await Promise.all(selectedRows.map((id)=>_lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.delete(String(id))));\n            setStudents((prev)=>prev.filter((student)=>!selectedRows.includes(String(student.id))));\n            toast({\n                title: \"Students Deleted\",\n                description: \"\".concat(selectedRows.length, \" students have been deleted successfully.\")\n            });\n            setSelectedRows([]);\n        } catch (error) {\n            console.error('Error deleting students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete students. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportStudents = async ()=>{\n        try {\n            if (students.length === 0) {\n                toast({\n                    title: \"No Data\",\n                    description: \"No students to export.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Generate CSV export\n            const csvData = students.map((student)=>({\n                    'Student ID': student.student_id || '',\n                    'Name': \"\".concat(student.first_name, \" \").concat(student.last_name),\n                    'Email': student.email || '',\n                    'Phone': student.phone || '',\n                    'Gender': student.gender || '',\n                    'Date of Birth': student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : '',\n                    'Blood Group': student.blood_group || '',\n                    'Nationality': student.nationality || '',\n                    'Religion': student.religion || '',\n                    'Address': student.address || '',\n                    'Emergency Contact': student.emergency_contact_name || '',\n                    'Emergency Phone': student.emergency_contact_phone || '',\n                    'Class': student.class_name || '',\n                    'Grade Level': student.grade_level || '',\n                    'Academic Year': student.academic_year || '',\n                    'Status': student.user_status || '',\n                    'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '',\n                    'Admission Number': student.admission_number || '',\n                    'Roll Number': student.roll_number || '',\n                    'Medical Conditions': student.medical_conditions || '',\n                    'Transport Required': student.transport_required ? 'Yes' : 'No',\n                    'Hostel Required': student.hostel_required ? 'Yes' : 'No'\n                }));\n            // Helper function to escape CSV values\n            const escapeCSV = (value)=>{\n                if (value === null || value === undefined) return '';\n                const str = String(value);\n                if (str.includes(',') || str.includes('\"') || str.includes('\\n')) {\n                    return '\"'.concat(str.replace(/\"/g, '\"\"'), '\"');\n                }\n                return str;\n            };\n            const csvContent = [\n                Object.keys(csvData[0]).join(','),\n                ...csvData.map((row)=>Object.values(row).map(escapeCSV).join(','))\n            ].join('\\n');\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"students_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            a.click();\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Export Successful\",\n                description: \"Student data has been exported successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to export student data.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImportStudents = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = '.csv';\n        input.onchange = async (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (!file) return;\n            try {\n                const text = await file.text();\n                const lines = text.split('\\n').filter((line)=>line.trim());\n                if (lines.length < 2) {\n                    toast({\n                        title: \"Invalid File\",\n                        description: \"CSV file must have at least a header and one data row.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Parse CSV (basic implementation)\n                const headers = lines[0].split(',').map((h)=>h.trim());\n                const requiredHeaders = [\n                    'firstName',\n                    'lastName',\n                    'email',\n                    'dateOfBirth',\n                    'gender'\n                ];\n                const missingHeaders = requiredHeaders.filter((h)=>!headers.includes(h));\n                if (missingHeaders.length > 0) {\n                    toast({\n                        title: \"Invalid CSV Format\",\n                        description: \"Missing required columns: \".concat(missingHeaders.join(', ')),\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                const students = lines.slice(1).map((line)=>{\n                    const values = line.split(',').map((v)=>v.trim().replace(/^\"|\"$/g, ''));\n                    const student = {};\n                    headers.forEach((header, index)=>{\n                        student[header] = values[index] || '';\n                    });\n                    return student;\n                });\n                // Validate and import students\n                const validStudents = students.filter((student)=>student.firstName && student.lastName && student.email && student.dateOfBirth && student.gender);\n                if (validStudents.length === 0) {\n                    toast({\n                        title: \"No Valid Students\",\n                        description: \"No valid student records found in the CSV file.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Use bulk create API\n                await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.bulkCreate(validStudents);\n                toast({\n                    title: \"Import Successful\",\n                    description: \"Successfully imported \".concat(validStudents.length, \" students.\")\n                });\n                // Refresh the students list\n                await fetchStudents();\n            } catch (error) {\n                console.error('Import error:', error);\n                toast({\n                    title: \"Import Failed\",\n                    description: \"Failed to import students. Please check the file format.\",\n                    variant: \"destructive\"\n                });\n            }\n        };\n        input.click();\n    };\n    const handlePrintAll = ()=>{\n        if (students.length === 0) {\n            toast({\n                title: \"No Data\",\n                description: \"No students to print.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create a new window for printing\n        const printWindow = window.open('', '_blank');\n        if (!printWindow) return;\n        const printContent = '\\n      <!DOCTYPE html>\\n      <html>\\n        <head>\\n          <title>Students List</title>\\n          <style>\\n            body {\\n              font-family: Arial, sans-serif;\\n              margin: 20px;\\n              line-height: 1.4;\\n            }\\n            .header {\\n              text-align: center;\\n              border-bottom: 2px solid #333;\\n              padding-bottom: 20px;\\n              margin-bottom: 30px;\\n            }\\n            table {\\n              width: 100%;\\n              border-collapse: collapse;\\n              margin-top: 20px;\\n            }\\n            th, td {\\n              border: 1px solid #ddd;\\n              padding: 8px;\\n              text-align: left;\\n              font-size: 12px;\\n            }\\n            th {\\n              background-color: #f5f5f5;\\n              font-weight: bold;\\n            }\\n            .footer {\\n              margin-top: 30px;\\n              text-align: center;\\n              font-size: 10px;\\n              color: #666;\\n            }\\n            @media print {\\n              body { margin: 0; }\\n              .no-print { display: none; }\\n            }\\n          </style>\\n        </head>\\n        <body>\\n          <div class=\"header\">\\n            <h1>Students List</h1>\\n            <p>Total Students: '.concat(students.length, \"</p>\\n            <p>Generated on: \").concat(new Date().toLocaleDateString(), \"</p>\\n          </div>\\n\\n          <table>\\n            <thead>\\n              <tr>\\n                <th>Student ID</th>\\n                <th>Name</th>\\n                <th>Email</th>\\n                <th>Phone</th>\\n                <th>Class</th>\\n                <th>Status</th>\\n                <th>Admission Date</th>\\n              </tr>\\n            </thead>\\n            <tbody>\\n              \").concat(students.map((student)=>\"\\n                <tr>\\n                  <td>\".concat(student.student_id || '', \"</td>\\n                  <td>\").concat(student.first_name, \" \").concat(student.last_name, \"</td>\\n                  <td>\").concat(student.email || '', \"</td>\\n                  <td>\").concat(student.phone || '', \"</td>\\n                  <td>\").concat(student.class_name || '', \"</td>\\n                  <td>\").concat(student.user_status || '', \"</td>\\n                  <td>\").concat(student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '', \"</td>\\n                </tr>\\n              \")).join(''), '\\n            </tbody>\\n          </table>\\n\\n          <div class=\"footer\">\\n            <p>School Management System - Students Report</p>\\n          </div>\\n        </body>\\n      </html>\\n    ');\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        printWindow.focus();\n        // Wait for content to load then print\n        setTimeout(()=>{\n            printWindow.print();\n            printWindow.close();\n        }, 250);\n        toast({\n            title: \"Print Started\",\n            description: \"Preparing student list for printing.\"\n        });\n    };\n    const columns = [\n        {\n            id: \"select\",\n            header: (param)=>{\n                let { table } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected() && \"indeterminate\",\n                    onCheckedChange: (value)=>table.toggleAllPageRowsSelected(!!value),\n                    \"aria-label\": \"Select all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 530,\n                    columnNumber: 9\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: row.getIsSelected(),\n                    onCheckedChange: (value)=>row.toggleSelected(!!value),\n                    \"aria-label\": \"Select row\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 537,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false,\n            enableHiding: false\n        },\n        {\n            accessorKey: \"student_id\",\n            header: \"Student ID\"\n        },\n        {\n            accessorKey: \"photo\",\n            header: \"Photo\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                    className: \"h-10 w-10\",\n                    children: student.profile_picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: student.profile_picture || \"/placeholder.svg\",\n                        alt: \"\".concat(student.first_name, \" \").concat(student.last_name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        children: [\n                            student.first_name.charAt(0),\n                            student.last_name.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 556,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"first_name\",\n            header: \"Name\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return \"\".concat(student.first_name, \" \").concat(student.last_name);\n            }\n        },\n        {\n            accessorKey: \"class_name\",\n            header: \"Class\"\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Email\",\n            cell: (param)=>{\n                let { row } = param;\n                const email = row.getValue(\"email\");\n                return email || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: \"Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const phone = row.getValue(\"phone\");\n                return phone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"gender\",\n            header: \"Gender\",\n            cell: (param)=>{\n                let { row } = param;\n                const gender = row.getValue(\"gender\");\n                return gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"date_of_birth\",\n            header: \"Date of Birth\",\n            cell: (param)=>{\n                let { row } = param;\n                const dob = row.getValue(\"date_of_birth\");\n                return dob ? new Date(dob).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"admission_date\",\n            header: \"Admission Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const admissionDate = row.getValue(\"admission_date\");\n                return admissionDate ? new Date(admissionDate).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_name\",\n            header: \"Emergency Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const contact = row.getValue(\"emergency_contact_name\");\n                return contact || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_phone\",\n            header: \"Emergency Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const contactPhone = row.getValue(\"emergency_contact_phone\");\n                return contactPhone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"user_status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const status = row.getValue(\"user_status\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: status === \"active\" ? \"default\" : \"outline\",\n                    children: status || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 639,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/students/\".concat(student.id)),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 15\n                                }, this),\n                                \"Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 648,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_12__.StudentDocumentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Docs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_13__.PrintStudentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 662,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__.AddStudentModal, {\n                            student: student,\n                            mode: \"edit\",\n                            onUpdate: handleSaveStudent,\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 677,\n                                columnNumber: 17\n                            }, void 0)\n                        }, \"edit-\".concat(student.id), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 671,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_9__.DeleteConfirmationModal, {\n                            title: \"Delete Student\",\n                            description: \"Are you sure you want to delete \".concat(student.first_name, \" \").concat(student.last_name, \"? This action cannot be undone.\"),\n                            onConfirm: ()=>handleDeleteStudent(student.id),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 687,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 647,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 702,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Loading students...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 703,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm text-muted-foreground\",\n                        children: \"Please wait while we fetch the student data.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 704,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 701,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n            lineNumber: 700,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 713,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleImportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleExportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 719,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrintAll,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Print All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 723,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__.AddStudentModal, {\n                                onAdd: handleAddStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Add Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 727,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 714,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 712,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"View and manage all students in the system\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__.BulkDeleteConfirmationModal, {\n                                    title: \"Delete Selected Students\",\n                                    description: \"Are you sure you want to delete \".concat(selectedRows.length, \" selected students? This action cannot be undone.\"),\n                                    count: selectedRows.length,\n                                    onConfirm: handleBulkDelete\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 739,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-gray-600\",\n                                        children: \"Loading students...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 758,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 756,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 755,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                            columns: columns,\n                            data: students,\n                            searchKey: \"first_name\",\n                            searchPlaceholder: \"Search students...\",\n                            onPrint: handlePrintAll,\n                            onExport: handleExportStudents\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 762,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 753,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 738,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n        lineNumber: 711,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentsPage, \"2mPRm9Jx2cynZ5f7wAONxa8oUKE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = StudentsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/students/page.tsx\n"));

/***/ })

});