"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./components/modals/add-student-modal.tsx":
/*!*************************************************!*\
  !*** ./components/modals/add-student-modal.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddStudentModal: () => (/* binding */ AddStudentModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/src/components/ui/file-upload */ \"(app-pages-browser)/./src/components/ui/file-upload.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _src_lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/src/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/v3/types.js\");\n/* __next_internal_client_entry_do_not_use__ AddStudentModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Backend-compatible student schema\nconst backendStudentSchema = zod__WEBPACK_IMPORTED_MODULE_12__.object({\n    firstName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'First name is required').max(100, 'First name must be less than 100 characters'),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Last name is required').max(100, 'Last name must be less than 100 characters'),\n    middleName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Middle name must be less than 100 characters').optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_12__.string().email('Please enter a valid email').optional(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    dateOfBirth: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Date of birth is required'),\n    gender: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'male',\n        'female',\n        'other'\n    ]),\n    bloodGroup: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'A+',\n        'A-',\n        'B+',\n        'B-',\n        'AB+',\n        'AB-',\n        'O+',\n        'O-'\n    ]).optional(),\n    nationality: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Nationality must be less than 100 characters').optional(),\n    religion: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Religion must be less than 100 characters').optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(500, 'Address must be less than 500 characters').optional(),\n    emergencyContactName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(200, 'Emergency contact name must be less than 200 characters').optional(),\n    emergencyContactPhone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    emergencyContactRelationship: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Emergency contact relationship must be less than 100 characters').optional(),\n    admissionDate: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Admission date is required'),\n    admissionNumber: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(50, 'Admission number must be less than 50 characters').optional(),\n    currentClassId: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Please select a class'),\n    academicYearId: zod__WEBPACK_IMPORTED_MODULE_12__.string().uuid('Academic year ID must be a valid UUID').optional(),\n    medicalConditions: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Medical conditions must be less than 1000 characters').optional(),\n    allergies: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Allergies must be less than 1000 characters').optional(),\n    generatePassword: zod__WEBPACK_IMPORTED_MODULE_12__.boolean().default(true),\n    password: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional()\n});\nfunction AddStudentModal(param) {\n    let { onAdd, onUpdate, student, mode = 'add', trigger } = param;\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingClasses, setLoadingClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedStudentId, setGeneratedStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const isEditMode = mode === 'edit' && student;\n    const { register, handleSubmit, setValue, reset, watch, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(backendStudentSchema),\n        defaultValues: {\n            gender: 'male',\n            admissionDate: new Date().toISOString().split('T')[0],\n            generatePassword: true\n        }\n    });\n    // Fetch classes and generate student ID preview when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddStudentModal.useEffect\": ()=>{\n            if (open) {\n                fetchClasses();\n                if (!isEditMode) {\n                    generateStudentIdPreview();\n                }\n            }\n        }\n    }[\"AddStudentModal.useEffect\"], [\n        open,\n        isEditMode\n    ]);\n    // Populate form with student data when in edit mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddStudentModal.useEffect\": ()=>{\n            if (isEditMode && student && open) {\n                // Convert student data to form format\n                const formData = {\n                    firstName: student.first_name || '',\n                    lastName: student.last_name || '',\n                    middleName: student.middle_name || '',\n                    email: student.email || '',\n                    phone: student.phone || '',\n                    dateOfBirth: student.date_of_birth ? student.date_of_birth.split('T')[0] : '',\n                    gender: student.gender || 'male',\n                    bloodGroup: student.blood_group || '',\n                    nationality: student.nationality || '',\n                    religion: student.religion || '',\n                    address: student.address || '',\n                    emergencyContactName: student.emergency_contact_name || '',\n                    emergencyContactPhone: student.emergency_contact_phone || '',\n                    emergencyContactRelationship: student.emergency_contact_relation || '',\n                    admissionDate: student.admission_date ? student.admission_date.split('T')[0] : '',\n                    admissionNumber: student.admission_number || '',\n                    currentClassId: student.current_class_id || student.class_id || '',\n                    medicalConditions: student.medical_conditions || '',\n                    allergies: student.allergies || '',\n                    generatePassword: false,\n                    password: ''\n                };\n                // Set form values\n                Object.entries(formData).forEach({\n                    \"AddStudentModal.useEffect\": (param)=>{\n                        let [key, value] = param;\n                        setValue(key, value);\n                    }\n                }[\"AddStudentModal.useEffect\"]);\n            }\n        }\n    }[\"AddStudentModal.useEffect\"], [\n        isEditMode,\n        student,\n        open,\n        setValue\n    ]);\n    const fetchClasses = async ()=>{\n        try {\n            setLoadingClasses(true);\n            const response = await _src_lib_api__WEBPACK_IMPORTED_MODULE_11__.classesApi.getAll({\n                status: 'active',\n                limit: 100,\n                sort_by: 'name',\n                sort_order: 'ASC'\n            });\n            if (response.success && response.data) {\n                var _classesArray_, _classesArray_1;\n                // Handle different possible response structures\n                let classesArray = [];\n                const data = response.data;\n                if (data.classes && Array.isArray(data.classes)) {\n                    classesArray = data.classes;\n                } else if (Array.isArray(response.data)) {\n                    classesArray = response.data;\n                }\n                console.log('First class structure:', classesArray[0]);\n                console.log('Available fields:', Object.keys(classesArray[0] || {}));\n                console.log('ID field:', (_classesArray_ = classesArray[0]) === null || _classesArray_ === void 0 ? void 0 : _classesArray_.id, 'UUID field:', (_classesArray_1 = classesArray[0]) === null || _classesArray_1 === void 0 ? void 0 : _classesArray_1.uuid);\n                setClasses(classesArray);\n                if (classesArray.length === 0) {\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                        title: \"No Classes Found\",\n                        description: \"No active classes are available. Please contact an administrator.\",\n                        variant: \"destructive\"\n                    });\n                }\n            } else {\n                setClasses([]);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Warning\",\n                    description: \"Failed to load classes. Please refresh and try again.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching classes:', error);\n            setClasses([]);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Error\",\n                description: \"Failed to load classes. Please check your connection.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoadingClasses(false);\n        }\n    };\n    const generateStudentIdPreview = ()=>{\n        const currentYear = new Date().getFullYear();\n        // This is just a preview - the actual ID will be generated by the backend\n        setGeneratedStudentId(\"STU-\".concat(currentYear, \"####\"));\n    };\n    const onSubmit = async (data)=>{\n        try {\n            setIsSubmitting(true);\n            console.log('Form data being submitted:', data);\n            console.log('Selected class ID:', data.currentClassId, 'Type:', typeof data.currentClassId);\n            // Validate required fields\n            if (!data.currentClassId) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Validation Error\",\n                    description: \"Please select a class for the student.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Find selected class for better success message\n            const selectedClass = classes.find((cls)=>cls.id === data.currentClassId);\n            if (isEditMode && onUpdate) {\n                // Convert form data back to student format for update\n                const updateData = {\n                    id: student.id,\n                    first_name: data.firstName,\n                    last_name: data.lastName,\n                    middle_name: data.middleName,\n                    email: data.email,\n                    phone: data.phone,\n                    date_of_birth: data.dateOfBirth,\n                    gender: data.gender,\n                    blood_group: data.bloodGroup,\n                    nationality: data.nationality,\n                    religion: data.religion,\n                    address: data.address,\n                    emergency_contact_name: data.emergencyContactName,\n                    emergency_contact_phone: data.emergencyContactPhone,\n                    emergency_contact_relation: data.emergencyContactRelationship,\n                    admission_date: data.admissionDate,\n                    admission_number: data.admissionNumber,\n                    current_class_id: data.currentClassId,\n                    medical_conditions: data.medicalConditions,\n                    allergies: data.allergies\n                };\n                console.log('Update data being sent to backend:', updateData);\n                await onUpdate(updateData);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Student Updated Successfully! ✅\",\n                    description: \"\".concat(data.firstName, \" \").concat(data.lastName, \"'s information has been updated.\")\n                });\n            } else if (onAdd) {\n                console.log('Form data being sent to backend:', data);\n                await onAdd(data);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Student Added Successfully! 🎉\",\n                    description: \"\".concat(data.firstName, \" \").concat(data.lastName, \" has been enrolled in \").concat((selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.name) || 'the selected class', \".\")\n                });\n            }\n            // Reset form and close modal\n            reset();\n            setOpen(false);\n        } catch (error) {\n            var _error_response_data, _error_response, _error_response_data1, _error_response1;\n            console.error('Error adding student:', error);\n            // Handle validation errors from backend\n            if ((error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.errors) && Array.isArray(error.response.data.errors)) {\n                const validationErrors = error.response.data.errors;\n                const classError = validationErrors.find((err)=>err.field === 'currentClassId');\n                if (classError) {\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                        title: \"Class Selection Error\",\n                        description: \"The selected class is invalid. Please select a different class and try again.\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    // Show first validation error\n                    const firstError = validationErrors[0];\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                        title: \"Validation Error\",\n                        description: \"\".concat(firstError.message || 'Please check your input and try again.'),\n                        variant: \"destructive\"\n                    });\n                }\n            } else if (error === null || error === void 0 ? void 0 : (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) {\n                // Handle general error messages\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Error\",\n                    description: error.response.data.message,\n                    variant: \"destructive\"\n                });\n            } else {\n                // Handle unknown errors\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Error\",\n                    description: \"Failed to \".concat(isEditMode ? 'update' : 'add', \" student. Please try again.\"),\n                    variant: \"destructive\"\n                });\n            }\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Reset form when modal is closed\n    const handleModalClose = (isOpen)=>{\n        setOpen(isOpen);\n        if (!isOpen) {\n            reset();\n            setGeneratedStudentId('');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n        open: open,\n        onOpenChange: handleModalClose,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTrigger, {\n                asChild: true,\n                children: trigger || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, this),\n                        \"Add Student\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                className: \"max-h-[90vh] max-w-[800px] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                children: isEditMode ? 'Edit Student' : 'Add New Student'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                children: isEditMode ? 'Update the student information below. Click save when you\\'re done.' : 'Enter the student information below. Click save when you\\'re done.'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit(onSubmit),\n                        className: \"space-y-4 mt-4\",\n                        children: [\n                            !isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-blue-800\",\n                                                children: \"Student ID Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-600 mt-1\",\n                                        children: [\n                                            \"Student ID will be automatically generated: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono font-semibold\",\n                                                children: generatedStudentId\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 61\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-500 mt-1\",\n                                        children: \"The actual ID will be assigned when the student is created\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this),\n                            isEditMode && student && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-green-800\",\n                                                children: \"Current Student ID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-600 mt-1\",\n                                        children: [\n                                            \"Student ID: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono font-semibold\",\n                                                children: student.student_id\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this),\n                                    student.admission_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-green-500 mt-1\",\n                                        children: [\n                                            \"Admission Number: \",\n                                            student.admission_number\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionNumber\",\n                                                children: \"Admission Number (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionNumber\",\n                                                ...register('admissionNumber'),\n                                                className: errors.admissionNumber ? 'border-red-500' : '',\n                                                placeholder: \"Leave empty to auto-generate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionNumber.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"currentClassId\",\n                                                        children: \"Class *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: fetchClasses,\n                                                        disabled: loadingClasses,\n                                                        className: \"h-6 px-2 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1 \".concat(loadingClasses ? 'animate-spin' : '')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Refresh\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                ...register('currentClassId'),\n                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 \".concat(errors.currentClassId ? 'border-red-500' : ''),\n                                                disabled: loadingClasses,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: loadingClasses ? \"Loading classes...\" : \"Select class\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    classes.map((cls)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: cls.id,\n                                                            children: cls.grade_level ? \"\".concat(cls.name, \" (\").concat(cls.grade_level, \")\") : cls.name\n                                                        }, cls.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.currentClassId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.currentClassId.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this),\n                                            classes.length === 0 && !loadingClasses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-amber-600\",\n                                                children: \"No classes found. Please contact an administrator to create classes first.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"firstName\",\n                                                children: \"First Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"firstName\",\n                                                ...register('firstName'),\n                                                className: errors.firstName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.firstName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"middleName\",\n                                                children: \"Middle Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"middleName\",\n                                                ...register('middleName'),\n                                                className: errors.middleName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.middleName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.middleName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"lastName\",\n                                                children: \"Last Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"lastName\",\n                                                ...register('lastName'),\n                                                className: errors.lastName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.lastName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"email\",\n                                                children: \"Email (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                ...register('email'),\n                                                className: errors.email ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.email.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"phone\",\n                                                children: \"Phone (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"phone\",\n                                                ...register('phone'),\n                                                className: errors.phone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.phone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"dateOfBirth\",\n                                                children: \"Date of Birth *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"dateOfBirth\",\n                                                type: \"date\",\n                                                ...register('dateOfBirth'),\n                                                className: errors.dateOfBirth ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.dateOfBirth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.dateOfBirth.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"gender\",\n                                                children: \"Gender *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('gender') || 'male',\n                                                onValueChange: (value)=>setValue('gender', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.gender ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select gender\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"male\",\n                                                                children: \"Male\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"female\",\n                                                                children: \"Female\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"other\",\n                                                                children: \"Other\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.gender && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.gender.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionDate\",\n                                                children: \"Admission Date *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionDate\",\n                                                type: \"date\",\n                                                ...register('admissionDate'),\n                                                className: errors.admissionDate ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionDate.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"bloodGroup\",\n                                                children: \"Blood Group\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('bloodGroup') || '',\n                                                onValueChange: (value)=>setValue('bloodGroup', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.bloodGroup ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select blood group\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A+\",\n                                                                children: \"A+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A-\",\n                                                                children: \"A-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B+\",\n                                                                children: \"B+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B-\",\n                                                                children: \"B-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB+\",\n                                                                children: \"AB+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB-\",\n                                                                children: \"AB-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O+\",\n                                                                children: \"O+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O-\",\n                                                                children: \"O-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.bloodGroup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.bloodGroup.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"nationality\",\n                                                children: \"Nationality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"nationality\",\n                                                ...register('nationality'),\n                                                className: errors.nationality ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Indian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.nationality && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.nationality.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"religion\",\n                                                children: \"Religion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"religion\",\n                                                ...register('religion'),\n                                                className: errors.religion ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.religion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.religion.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"address\",\n                                        children: \"Address (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        id: \"address\",\n                                        ...register('address'),\n                                        className: errors.address ? 'border-red-500' : ''\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600\",\n                                        children: errors.address.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactName\",\n                                                children: \"Emergency Contact Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactName\",\n                                                ...register('emergencyContactName'),\n                                                className: errors.emergencyContactName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactPhone\",\n                                                children: \"Emergency Contact Phone\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactPhone\",\n                                                ...register('emergencyContactPhone'),\n                                                className: errors.emergencyContactPhone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactPhone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactPhone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactRelationship\",\n                                                children: \"Relationship\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactRelationship\",\n                                                ...register('emergencyContactRelationship'),\n                                                className: errors.emergencyContactRelationship ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Father, Mother, Guardian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactRelationship && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactRelationship.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"medicalConditions\",\n                                                children: \"Medical Conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"medicalConditions\",\n                                                ...register('medicalConditions'),\n                                                className: errors.medicalConditions ? 'border-red-500' : '',\n                                                placeholder: \"Any medical conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.medicalConditions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.medicalConditions.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"allergies\",\n                                                children: \"Allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"allergies\",\n                                                ...register('allergies'),\n                                                className: errors.allergies ? 'border-red-500' : '',\n                                                placeholder: \"Any known allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.allergies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.allergies.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 11\n                            }, this),\n                            !isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 border-t pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Password Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                id: \"generatePassword\",\n                                                checked: watch('generatePassword'),\n                                                onCheckedChange: (checked)=>setValue('generatePassword', !!checked)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"generatePassword\",\n                                                className: \"text-sm\",\n                                                children: \"Generate password automatically (recommended)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 15\n                                    }, this),\n                                    !watch('generatePassword') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"password\",\n                                                children: \"Custom Password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"password\",\n                                                type: \"password\",\n                                                ...register('password'),\n                                                className: errors.password ? 'border-red-500' : '',\n                                                placeholder: \"Enter custom password (min 8 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 19\n                                            }, this),\n                                            errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.password.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"profile_picture\",\n                                        children: \"Profile Picture (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__.FileUpload, {\n                                        accept: \"image/*\",\n                                        maxFiles: 1,\n                                        maxSize: 5 * 1024 * 1024,\n                                        autoUpload: false,\n                                        onFileSelect: (files)=>{\n                                            // Handle file selection for future implementation\n                                            console.log('Selected files:', files);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Upload a profile picture for the student (max 5MB)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: ()=>setOpen(false),\n                                        disabled: isSubmitting,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isEditMode ? 'Updating...' : 'Adding...'\n                                            ]\n                                        }, void 0, true) : isEditMode ? 'Update Student' : 'Add Student'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 742,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n        lineNumber: 333,\n        columnNumber: 5\n    }, this);\n}\n_s(AddStudentModal, \"/v7JLUIS8G/it21M1VUDKZENywk=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm\n    ];\n});\n_c = AddStudentModal;\nvar _c;\n$RefreshReg$(_c, \"AddStudentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvbW9kYWxzL2FkZC1zdHVkZW50LW1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJMkM7QUFDRjtBQUNZO0FBQ047QUFTaEI7QUFDYztBQUNBO0FBQ3lEO0FBQ25EO0FBQ1M7QUFDWDtBQUNQO0FBQ2E7QUFDL0I7QUFJeEIsb0NBQW9DO0FBQ3BDLE1BQU0yQix1QkFBdUJELHdDQUFRLENBQUM7SUFDcENHLFdBQVdILHdDQUFRLEdBQUdLLEdBQUcsQ0FBQyxHQUFHLDBCQUEwQkMsR0FBRyxDQUFDLEtBQUs7SUFDaEVDLFVBQVVQLHdDQUFRLEdBQUdLLEdBQUcsQ0FBQyxHQUFHLHlCQUF5QkMsR0FBRyxDQUFDLEtBQUs7SUFDOURFLFlBQVlSLHdDQUFRLEdBQUdNLEdBQUcsQ0FBQyxLQUFLLGdEQUFnREcsUUFBUTtJQUN4RkMsT0FBT1Ysd0NBQVEsR0FBR1UsS0FBSyxDQUFDLDhCQUE4QkQsUUFBUTtJQUM5REUsT0FBT1gsd0NBQVEsR0FBR1MsUUFBUTtJQUMxQkcsYUFBYVosd0NBQVEsR0FBR0ssR0FBRyxDQUFDLEdBQUc7SUFDL0JRLFFBQVFiLHlDQUFNLENBQUM7UUFBQztRQUFRO1FBQVU7S0FBUTtJQUMxQ2UsWUFBWWYseUNBQU0sQ0FBQztRQUFDO1FBQU07UUFBTTtRQUFNO1FBQU07UUFBTztRQUFPO1FBQU07S0FBSyxFQUFFUyxRQUFRO0lBQy9FTyxhQUFhaEIsd0NBQVEsR0FBR00sR0FBRyxDQUFDLEtBQUssZ0RBQWdERyxRQUFRO0lBQ3pGUSxVQUFVakIsd0NBQVEsR0FBR00sR0FBRyxDQUFDLEtBQUssNkNBQTZDRyxRQUFRO0lBQ25GUyxTQUFTbEIsd0NBQVEsR0FBR00sR0FBRyxDQUFDLEtBQUssNENBQTRDRyxRQUFRO0lBQ2pGVSxzQkFBc0JuQix3Q0FBUSxHQUFHTSxHQUFHLENBQUMsS0FBSywyREFBMkRHLFFBQVE7SUFDN0dXLHVCQUF1QnBCLHdDQUFRLEdBQUdTLFFBQVE7SUFDMUNZLDhCQUE4QnJCLHdDQUFRLEdBQUdNLEdBQUcsQ0FBQyxLQUFLLG1FQUFtRUcsUUFBUTtJQUM3SGEsZUFBZXRCLHdDQUFRLEdBQUdLLEdBQUcsQ0FBQyxHQUFHO0lBQ2pDa0IsaUJBQWlCdkIsd0NBQVEsR0FBR00sR0FBRyxDQUFDLElBQUksb0RBQW9ERyxRQUFRO0lBQ2hHZSxnQkFBZ0J4Qix3Q0FBUSxHQUFHSyxHQUFHLENBQUMsR0FBRztJQUNsQ29CLGdCQUFnQnpCLHdDQUFRLEdBQUcwQixJQUFJLENBQUMseUNBQXlDakIsUUFBUTtJQUNqRmtCLG1CQUFtQjNCLHdDQUFRLEdBQUdNLEdBQUcsQ0FBQyxNQUFNLHdEQUF3REcsUUFBUTtJQUN4R21CLFdBQVc1Qix3Q0FBUSxHQUFHTSxHQUFHLENBQUMsTUFBTSwrQ0FBK0NHLFFBQVE7SUFDdkZvQixrQkFBa0I3Qix5Q0FBUyxHQUFHK0IsT0FBTyxDQUFDO0lBQ3RDQyxVQUFVaEMsd0NBQVEsR0FBR1MsUUFBUTtBQUMvQjtBQWdDTyxTQUFTd0IsZ0JBQWdCLEtBQXlFO1FBQXpFLEVBQUVDLEtBQUssRUFBRUMsUUFBUSxFQUFFQyxPQUFPLEVBQUVDLE9BQU8sS0FBSyxFQUFFQyxPQUFPLEVBQXdCLEdBQXpFOztJQUM5QixNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR2xFLCtDQUFRQSxDQUFDO0lBQ2pDLE1BQU0sQ0FBQ21FLGNBQWNDLGdCQUFnQixHQUFHcEUsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDcUUsU0FBU0MsV0FBVyxHQUFHdEUsK0NBQVFBLENBQWMsRUFBRTtJQUN0RCxNQUFNLENBQUN1RSxnQkFBZ0JDLGtCQUFrQixHQUFHeEUsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDeUUsb0JBQW9CQyxzQkFBc0IsR0FBRzFFLCtDQUFRQSxDQUFTO0lBRXJFLE1BQU0yRSxhQUFhWixTQUFTLFVBQVVEO0lBRXRDLE1BQU0sRUFDSmMsUUFBUSxFQUNSQyxZQUFZLEVBQ1pDLFFBQVEsRUFDUkMsS0FBSyxFQUNMQyxLQUFLLEVBQ0xDLFdBQVcsRUFBRUMsTUFBTSxFQUFFLEVBQ3RCLEdBQUdoRix5REFBT0EsQ0FBeUI7UUFDbENpRixVQUFVaEYsb0VBQVdBLENBQUN3QjtRQUN0QnlELGVBQWU7WUFDYjdDLFFBQVE7WUFDUlMsZUFBZSxJQUFJcUMsT0FBT0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDckRoQyxrQkFBa0I7UUFDcEI7SUFDRjtJQUVBLGlFQUFpRTtJQUNqRXRELGdEQUFTQTtxQ0FBQztZQUNSLElBQUlnRSxNQUFNO2dCQUNSdUI7Z0JBQ0EsSUFBSSxDQUFDYixZQUFZO29CQUNmYztnQkFDRjtZQUNGO1FBQ0Y7b0NBQUc7UUFBQ3hCO1FBQU1VO0tBQVc7SUFFckIsb0RBQW9EO0lBQ3BEMUUsZ0RBQVNBO3FDQUFDO1lBQ1IsSUFBSTBFLGNBQWNiLFdBQVdHLE1BQU07Z0JBQ2pDLHNDQUFzQztnQkFDdEMsTUFBTXlCLFdBQVc7b0JBQ2Y3RCxXQUFXaUMsUUFBUTZCLFVBQVUsSUFBSTtvQkFDakMxRCxVQUFVNkIsUUFBUThCLFNBQVMsSUFBSTtvQkFDL0IxRCxZQUFZNEIsUUFBUStCLFdBQVcsSUFBSTtvQkFDbkN6RCxPQUFPMEIsUUFBUTFCLEtBQUssSUFBSTtvQkFDeEJDLE9BQU95QixRQUFRekIsS0FBSyxJQUFJO29CQUN4QkMsYUFBYXdCLFFBQVFnQyxhQUFhLEdBQUdoQyxRQUFRZ0MsYUFBYSxDQUFDUCxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsR0FBRztvQkFDM0VoRCxRQUFRdUIsUUFBUXZCLE1BQU0sSUFBSTtvQkFDMUJFLFlBQVlxQixRQUFRaUMsV0FBVyxJQUFJO29CQUNuQ3JELGFBQWFvQixRQUFRcEIsV0FBVyxJQUFJO29CQUNwQ0MsVUFBVW1CLFFBQVFuQixRQUFRLElBQUk7b0JBQzlCQyxTQUFTa0IsUUFBUWxCLE9BQU8sSUFBSTtvQkFDNUJDLHNCQUFzQmlCLFFBQVFrQyxzQkFBc0IsSUFBSTtvQkFDeERsRCx1QkFBdUJnQixRQUFRbUMsdUJBQXVCLElBQUk7b0JBQzFEbEQsOEJBQThCZSxRQUFRb0MsMEJBQTBCLElBQUk7b0JBQ3BFbEQsZUFBZWMsUUFBUXFDLGNBQWMsR0FBR3JDLFFBQVFxQyxjQUFjLENBQUNaLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxHQUFHO29CQUMvRXRDLGlCQUFpQmEsUUFBUXNDLGdCQUFnQixJQUFJO29CQUM3Q2xELGdCQUFnQlksUUFBUXVDLGdCQUFnQixJQUFJdkMsUUFBUXdDLFFBQVEsSUFBSTtvQkFDaEVqRCxtQkFBbUJTLFFBQVF5QyxrQkFBa0IsSUFBSTtvQkFDakRqRCxXQUFXUSxRQUFRUixTQUFTLElBQUk7b0JBQ2hDQyxrQkFBa0I7b0JBQ2xCRyxVQUFVO2dCQUNaO2dCQUVBLGtCQUFrQjtnQkFDbEI4QyxPQUFPQyxPQUFPLENBQUNmLFVBQVVnQixPQUFPO2lEQUFDOzRCQUFDLENBQUNDLEtBQUtDLE1BQU07d0JBQzVDOUIsU0FBUzZCLEtBQXFDQztvQkFDaEQ7O1lBQ0Y7UUFDRjtvQ0FBRztRQUFDakM7UUFBWWI7UUFBU0c7UUFBTWE7S0FBUztJQUV4QyxNQUFNVSxlQUFlO1FBQ25CLElBQUk7WUFDRmhCLGtCQUFrQjtZQUVsQixNQUFNcUMsV0FBVyxNQUFNdkYscURBQVVBLENBQUN3RixNQUFNLENBQUM7Z0JBQ3ZDQyxRQUFRO2dCQUNSQyxPQUFPO2dCQUNQQyxTQUFTO2dCQUNUQyxZQUFZO1lBQ2Q7WUFFQSxJQUFJTCxTQUFTTSxPQUFPLElBQUlOLFNBQVNPLElBQUksRUFBRTtvQkFZWkMsZ0JBQW9DQTtnQkFYN0QsZ0RBQWdEO2dCQUNoRCxJQUFJQSxlQUFlLEVBQUU7Z0JBQ3JCLE1BQU1ELE9BQU9QLFNBQVNPLElBQUk7Z0JBQzFCLElBQUlBLEtBQUsvQyxPQUFPLElBQUlpRCxNQUFNQyxPQUFPLENBQUNILEtBQUsvQyxPQUFPLEdBQUc7b0JBQy9DZ0QsZUFBZUQsS0FBSy9DLE9BQU87Z0JBQzdCLE9BQU8sSUFBSWlELE1BQU1DLE9BQU8sQ0FBQ1YsU0FBU08sSUFBSSxHQUFHO29CQUN2Q0MsZUFBZVIsU0FBU08sSUFBSTtnQkFDOUI7Z0JBRUFJLFFBQVFDLEdBQUcsQ0FBQywwQkFBMEJKLFlBQVksQ0FBQyxFQUFFO2dCQUNyREcsUUFBUUMsR0FBRyxDQUFDLHFCQUFxQmpCLE9BQU9rQixJQUFJLENBQUNMLFlBQVksQ0FBQyxFQUFFLElBQUksQ0FBQztnQkFDakVHLFFBQVFDLEdBQUcsQ0FBQyxjQUFhSixpQkFBQUEsWUFBWSxDQUFDLEVBQUUsY0FBZkEscUNBQUFBLGVBQWlCTSxFQUFFLEVBQUUsZ0JBQWVOLGtCQUFBQSxZQUFZLENBQUMsRUFBRSxjQUFmQSxzQ0FBQUEsZ0JBQWlCakUsSUFBSTtnQkFDbEZrQixXQUFXK0M7Z0JBRVgsSUFBSUEsYUFBYU8sTUFBTSxLQUFLLEdBQUc7b0JBQzdCdkcsZ0VBQUtBLENBQUM7d0JBQ0p3RyxPQUFPO3dCQUNQQyxhQUFhO3dCQUNiQyxTQUFTO29CQUNYO2dCQUNGO1lBQ0YsT0FBTztnQkFDTHpELFdBQVcsRUFBRTtnQkFDYmpELGdFQUFLQSxDQUFDO29CQUNKd0csT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsU0FBUztnQkFDWDtZQUNGO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDMUQsV0FBVyxFQUFFO1lBQ2JqRCxnRUFBS0EsQ0FBQztnQkFDSndHLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGLFNBQVU7WUFDUnZELGtCQUFrQjtRQUNwQjtJQUNGO0lBRUEsTUFBTWlCLDJCQUEyQjtRQUMvQixNQUFNd0MsY0FBYyxJQUFJNUMsT0FBTzZDLFdBQVc7UUFDMUMsMEVBQTBFO1FBQzFFeEQsc0JBQXNCLE9BQW1CLE9BQVp1RCxhQUFZO0lBQzNDO0lBRUEsTUFBTUUsV0FBVyxPQUFPZjtRQUN0QixJQUFJO1lBQ0ZoRCxnQkFBZ0I7WUFDaEJvRCxRQUFRQyxHQUFHLENBQUMsOEJBQThCTDtZQUMxQ0ksUUFBUUMsR0FBRyxDQUFDLHNCQUFzQkwsS0FBS2xFLGNBQWMsRUFBRSxTQUFTLE9BQU9rRSxLQUFLbEUsY0FBYztZQUUxRiwyQkFBMkI7WUFDM0IsSUFBSSxDQUFDa0UsS0FBS2xFLGNBQWMsRUFBRTtnQkFDeEI3QixnRUFBS0EsQ0FBQztvQkFDSndHLE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2JDLFNBQVM7Z0JBQ1g7Z0JBQ0E7WUFDRjtZQUVBLGlEQUFpRDtZQUNqRCxNQUFNSyxnQkFBZ0IvRCxRQUFRZ0UsSUFBSSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJWCxFQUFFLEtBQUtQLEtBQUtsRSxjQUFjO1lBRXhFLElBQUl5QixjQUFjZCxVQUFVO2dCQUMxQixzREFBc0Q7Z0JBQ3RELE1BQU0wRSxhQUFhO29CQUNqQlosSUFBSTdELFFBQVE2RCxFQUFFO29CQUNkaEMsWUFBWXlCLEtBQUt2RixTQUFTO29CQUMxQitELFdBQVd3QixLQUFLbkYsUUFBUTtvQkFDeEI0RCxhQUFhdUIsS0FBS2xGLFVBQVU7b0JBQzVCRSxPQUFPZ0YsS0FBS2hGLEtBQUs7b0JBQ2pCQyxPQUFPK0UsS0FBSy9FLEtBQUs7b0JBQ2pCeUQsZUFBZXNCLEtBQUs5RSxXQUFXO29CQUMvQkMsUUFBUTZFLEtBQUs3RSxNQUFNO29CQUNuQndELGFBQWFxQixLQUFLM0UsVUFBVTtvQkFDNUJDLGFBQWEwRSxLQUFLMUUsV0FBVztvQkFDN0JDLFVBQVV5RSxLQUFLekUsUUFBUTtvQkFDdkJDLFNBQVN3RSxLQUFLeEUsT0FBTztvQkFDckJvRCx3QkFBd0JvQixLQUFLdkUsb0JBQW9CO29CQUNqRG9ELHlCQUF5Qm1CLEtBQUt0RSxxQkFBcUI7b0JBQ25Eb0QsNEJBQTRCa0IsS0FBS3JFLDRCQUE0QjtvQkFDN0RvRCxnQkFBZ0JpQixLQUFLcEUsYUFBYTtvQkFDbENvRCxrQkFBa0JnQixLQUFLbkUsZUFBZTtvQkFDdENvRCxrQkFBa0JlLEtBQUtsRSxjQUFjO29CQUNyQ3FELG9CQUFvQmEsS0FBSy9ELGlCQUFpQjtvQkFDMUNDLFdBQVc4RCxLQUFLOUQsU0FBUztnQkFDM0I7Z0JBRUFrRSxRQUFRQyxHQUFHLENBQUMsc0NBQXNDYztnQkFDbEQsTUFBTTFFLFNBQVMwRTtnQkFFZmxILGdFQUFLQSxDQUFDO29CQUNKd0csT0FBTztvQkFDUEMsYUFBYSxHQUFxQlYsT0FBbEJBLEtBQUt2RixTQUFTLEVBQUMsS0FBaUIsT0FBZHVGLEtBQUtuRixRQUFRLEVBQUM7Z0JBQ2xEO1lBQ0YsT0FBTyxJQUFJMkIsT0FBTztnQkFDaEI0RCxRQUFRQyxHQUFHLENBQUMsb0NBQW9DTDtnQkFDaEQsTUFBTXhELE1BQU13RDtnQkFFWi9GLGdFQUFLQSxDQUFDO29CQUNKd0csT0FBTztvQkFDUEMsYUFBYSxHQUFxQlYsT0FBbEJBLEtBQUt2RixTQUFTLEVBQUMsS0FBeUN1RyxPQUF0Q2hCLEtBQUtuRixRQUFRLEVBQUMsMEJBQW9FLE9BQTVDbUcsQ0FBQUEsMEJBQUFBLG9DQUFBQSxjQUFlSSxJQUFJLEtBQUksc0JBQXFCO2dCQUN0SDtZQUNGO1lBRUEsNkJBQTZCO1lBQzdCekQ7WUFDQWIsUUFBUTtRQUNWLEVBQUUsT0FBTzhELE9BQVk7Z0JBSWZBLHNCQUFBQSxpQkFtQk9BLHVCQUFBQTtZQXRCWFIsUUFBUVEsS0FBSyxDQUFDLHlCQUF5QkE7WUFFdkMsd0NBQXdDO1lBQ3hDLElBQUlBLENBQUFBLGtCQUFBQSw2QkFBQUEsa0JBQUFBLE1BQU9uQixRQUFRLGNBQWZtQix1Q0FBQUEsdUJBQUFBLGdCQUFpQlosSUFBSSxjQUFyQlksMkNBQUFBLHFCQUF1QjlDLE1BQU0sS0FBSW9DLE1BQU1DLE9BQU8sQ0FBQ1MsTUFBTW5CLFFBQVEsQ0FBQ08sSUFBSSxDQUFDbEMsTUFBTSxHQUFHO2dCQUM5RSxNQUFNdUQsbUJBQW1CVCxNQUFNbkIsUUFBUSxDQUFDTyxJQUFJLENBQUNsQyxNQUFNO2dCQUNuRCxNQUFNd0QsYUFBYUQsaUJBQWlCSixJQUFJLENBQUMsQ0FBQ00sTUFBYUEsSUFBSUMsS0FBSyxLQUFLO2dCQUVyRSxJQUFJRixZQUFZO29CQUNkckgsZ0VBQUtBLENBQUM7d0JBQ0p3RyxPQUFPO3dCQUNQQyxhQUFhO3dCQUNiQyxTQUFTO29CQUNYO2dCQUNGLE9BQU87b0JBQ0wsOEJBQThCO29CQUM5QixNQUFNYyxhQUFhSixnQkFBZ0IsQ0FBQyxFQUFFO29CQUN0Q3BILGdFQUFLQSxDQUFDO3dCQUNKd0csT0FBTzt3QkFDUEMsYUFBYSxHQUFrRSxPQUEvRGUsV0FBV0MsT0FBTyxJQUFJO3dCQUN0Q2YsU0FBUztvQkFDWDtnQkFDRjtZQUNGLE9BQU8sSUFBSUMsa0JBQUFBLDZCQUFBQSxtQkFBQUEsTUFBT25CLFFBQVEsY0FBZm1CLHdDQUFBQSx3QkFBQUEsaUJBQWlCWixJQUFJLGNBQXJCWSw0Q0FBQUEsc0JBQXVCYyxPQUFPLEVBQUU7Z0JBQ3pDLGdDQUFnQztnQkFDaEN6SCxnRUFBS0EsQ0FBQztvQkFDSndHLE9BQU87b0JBQ1BDLGFBQWFFLE1BQU1uQixRQUFRLENBQUNPLElBQUksQ0FBQzBCLE9BQU87b0JBQ3hDZixTQUFTO2dCQUNYO1lBQ0YsT0FBTztnQkFDTCx3QkFBd0I7Z0JBQ3hCMUcsZ0VBQUtBLENBQUM7b0JBQ0p3RyxPQUFPO29CQUNQQyxhQUFhLGFBQTJDLE9BQTlCbkQsYUFBYSxXQUFXLE9BQU07b0JBQ3hEb0QsU0FBUztnQkFDWDtZQUNGO1FBQ0YsU0FBVTtZQUNSM0QsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxrQ0FBa0M7SUFDbEMsTUFBTTJFLG1CQUFtQixDQUFDQztRQUN4QjlFLFFBQVE4RTtRQUNSLElBQUksQ0FBQ0EsUUFBUTtZQUNYakU7WUFDQUwsc0JBQXNCO1FBQ3hCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ3JFLHlEQUFNQTtRQUFDNEQsTUFBTUE7UUFBTWdGLGNBQWNGOzswQkFDaEMsOERBQUNwSSxnRUFBYUE7Z0JBQUN1SSxPQUFPOzBCQUNuQmxGLHlCQUNDLDhEQUFDNUQseURBQU1BOztzQ0FDTCw4REFBQ29CLG1HQUFJQTs0QkFBQzJILFdBQVU7Ozs7Ozt3QkFBaUI7Ozs7Ozs7Ozs7OzswQkFLdkMsOERBQUM3SSxnRUFBYUE7Z0JBQUM2SSxXQUFVOztrQ0FDdkIsOERBQUMxSSwrREFBWUE7OzBDQUNYLDhEQUFDQyw4REFBV0E7MENBQUVpRSxhQUFhLGlCQUFpQjs7Ozs7OzBDQUM1Qyw4REFBQ3BFLG9FQUFpQkE7MENBQ2ZvRSxhQUNHLHdFQUNBOzs7Ozs7Ozs7Ozs7a0NBS1IsOERBQUN5RTt3QkFBS2pCLFVBQVV0RCxhQUFhc0Q7d0JBQVdnQixXQUFVOzs0QkFHL0MsQ0FBQ3hFLDRCQUNBLDhEQUFDMEU7Z0NBQUlGLFdBQVU7O2tEQUNiLDhEQUFDRTt3Q0FBSUYsV0FBVTs7MERBQ2IsOERBQUNFO2dEQUFJRixXQUFVOzs7Ozs7MERBQ2YsOERBQUNHO2dEQUFLSCxXQUFVOzBEQUFvQzs7Ozs7Ozs7Ozs7O2tEQUV0RCw4REFBQ0k7d0NBQUVKLFdBQVU7OzRDQUE2QjswREFDSSw4REFBQ0c7Z0RBQUtILFdBQVU7MERBQTJCMUU7Ozs7Ozs7Ozs7OztrREFFekYsOERBQUM4RTt3Q0FBRUosV0FBVTtrREFBNkI7Ozs7Ozs7Ozs7Ozs0QkFPN0N4RSxjQUFjYix5QkFDYiw4REFBQ3VGO2dDQUFJRixXQUFVOztrREFDYiw4REFBQ0U7d0NBQUlGLFdBQVU7OzBEQUNiLDhEQUFDRTtnREFBSUYsV0FBVTs7Ozs7OzBEQUNmLDhEQUFDRztnREFBS0gsV0FBVTswREFBcUM7Ozs7Ozs7Ozs7OztrREFFdkQsOERBQUNJO3dDQUFFSixXQUFVOzs0Q0FBOEI7MERBQzdCLDhEQUFDRztnREFBS0gsV0FBVTswREFBMkJyRixRQUFRMEYsVUFBVTs7Ozs7Ozs7Ozs7O29DQUUxRTFGLFFBQVFzQyxnQkFBZ0Isa0JBQ3ZCLDhEQUFDbUQ7d0NBQUVKLFdBQVU7OzRDQUE4Qjs0Q0FDdEJyRixRQUFRc0MsZ0JBQWdCOzs7Ozs7Ozs7Ozs7OzBDQVVuRCw4REFBQ2lEO2dDQUFJRixXQUFVOztrREFDYiw4REFBQ0U7d0NBQUlGLFdBQVU7OzBEQUNiLDhEQUFDdEksdURBQUtBO2dEQUFDNEksU0FBUTswREFBa0I7Ozs7OzswREFDakMsOERBQUM3SSx1REFBS0E7Z0RBQ0orRyxJQUFHO2dEQUNGLEdBQUcvQyxTQUFTLGtCQUFrQjtnREFDL0J1RSxXQUFXakUsT0FBT2pDLGVBQWUsR0FBRyxtQkFBbUI7Z0RBQ3ZEeUcsYUFBWTs7Ozs7OzRDQUVieEUsT0FBT2pDLGVBQWUsa0JBQ3JCLDhEQUFDc0c7Z0RBQUVKLFdBQVU7MERBQXdCakUsT0FBT2pDLGVBQWUsQ0FBQzZGLE9BQU87Ozs7Ozs7Ozs7OztrREFJdkUsOERBQUNPO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ0U7Z0RBQUlGLFdBQVU7O2tFQUNiLDhEQUFDdEksdURBQUtBO3dEQUFDNEksU0FBUTtrRUFBaUI7Ozs7OztrRUFDaEMsOERBQUNySix5REFBTUE7d0RBQ0x1SixNQUFLO3dEQUNMNUIsU0FBUTt3REFDUjZCLE1BQUs7d0RBQ0xDLFNBQVNyRTt3REFDVHNFLFVBQVV2Rjt3REFDVjRFLFdBQVU7OzBFQUVWLDhEQUFDMUgsbUdBQVNBO2dFQUFDMEgsV0FBVyxnQkFBcUQsT0FBckM1RSxpQkFBaUIsaUJBQWlCOzs7Ozs7NERBQVE7Ozs7Ozs7Ozs7Ozs7MERBSXBGLDhEQUFDd0Y7Z0RBQ0UsR0FBR25GLFNBQVMsaUJBQWlCO2dEQUM5QnVFLFdBQVcsZ1dBQThZLE9BQTlDakUsT0FBT2hDLGNBQWMsR0FBRyxtQkFBbUI7Z0RBQ3RaNEcsVUFBVXZGOztrRUFFViw4REFBQ3lGO3dEQUFPcEQsT0FBTTtrRUFDWHJDLGlCQUFpQix1QkFBdUI7Ozs7OztvREFFMUNGLFFBQVE0RixHQUFHLENBQUMsQ0FBQzNCLG9CQUNaLDhEQUFDMEI7NERBQW9CcEQsT0FBTzBCLElBQUlYLEVBQUU7c0VBQy9CVyxJQUFJNEIsV0FBVyxHQUFHLEdBQWdCNUIsT0FBYkEsSUFBSUUsSUFBSSxFQUFDLE1BQW9CLE9BQWhCRixJQUFJNEIsV0FBVyxFQUFDLE9BQUs1QixJQUFJRSxJQUFJOzJEQURyREYsSUFBSVgsRUFBRTs7Ozs7Ozs7Ozs7NENBTXRCekMsT0FBT2hDLGNBQWMsa0JBQ3BCLDhEQUFDcUc7Z0RBQUVKLFdBQVU7MERBQXdCakUsT0FBT2hDLGNBQWMsQ0FBQzRGLE9BQU87Ozs7Ozs0Q0FFbkV6RSxRQUFRdUQsTUFBTSxLQUFLLEtBQUssQ0FBQ3JELGdDQUN4Qiw4REFBQ2dGO2dEQUFFSixXQUFVOzBEQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU81Qyw4REFBQ0U7Z0NBQUlGLFdBQVU7O2tEQUNiLDhEQUFDRTt3Q0FBSUYsV0FBVTs7MERBQ2IsOERBQUN0SSx1REFBS0E7Z0RBQUM0SSxTQUFROzBEQUFZOzs7Ozs7MERBQzNCLDhEQUFDN0ksdURBQUtBO2dEQUNKK0csSUFBRztnREFDRixHQUFHL0MsU0FBUyxZQUFZO2dEQUN6QnVFLFdBQVdqRSxPQUFPckQsU0FBUyxHQUFHLG1CQUFtQjs7Ozs7OzRDQUVsRHFELE9BQU9yRCxTQUFTLGtCQUNmLDhEQUFDMEg7Z0RBQUVKLFdBQVU7MERBQXdCakUsT0FBT3JELFNBQVMsQ0FBQ2lILE9BQU87Ozs7Ozs7Ozs7OztrREFJakUsOERBQUNPO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ3RJLHVEQUFLQTtnREFBQzRJLFNBQVE7MERBQWE7Ozs7OzswREFDNUIsOERBQUM3SSx1REFBS0E7Z0RBQ0orRyxJQUFHO2dEQUNGLEdBQUcvQyxTQUFTLGFBQWE7Z0RBQzFCdUUsV0FBV2pFLE9BQU9oRCxVQUFVLEdBQUcsbUJBQW1COzs7Ozs7NENBRW5EZ0QsT0FBT2hELFVBQVUsa0JBQ2hCLDhEQUFDcUg7Z0RBQUVKLFdBQVU7MERBQXdCakUsT0FBT2hELFVBQVUsQ0FBQzRHLE9BQU87Ozs7Ozs7Ozs7OztrREFJbEUsOERBQUNPO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ3RJLHVEQUFLQTtnREFBQzRJLFNBQVE7MERBQVc7Ozs7OzswREFDMUIsOERBQUM3SSx1REFBS0E7Z0RBQ0orRyxJQUFHO2dEQUNGLEdBQUcvQyxTQUFTLFdBQVc7Z0RBQ3hCdUUsV0FBV2pFLE9BQU9qRCxRQUFRLEdBQUcsbUJBQW1COzs7Ozs7NENBRWpEaUQsT0FBT2pELFFBQVEsa0JBQ2QsOERBQUNzSDtnREFBRUosV0FBVTswREFBd0JqRSxPQUFPakQsUUFBUSxDQUFDNkcsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUtsRSw4REFBQ087Z0NBQUlGLFdBQVU7O2tEQUNiLDhEQUFDRTt3Q0FBSUYsV0FBVTs7MERBQ2IsOERBQUN0SSx1REFBS0E7Z0RBQUM0SSxTQUFROzBEQUFROzs7Ozs7MERBQ3ZCLDhEQUFDN0ksdURBQUtBO2dEQUNKK0csSUFBRztnREFDSGdDLE1BQUs7Z0RBQ0osR0FBRy9FLFNBQVMsUUFBUTtnREFDckJ1RSxXQUFXakUsT0FBTzlDLEtBQUssR0FBRyxtQkFBbUI7Ozs7Ozs0Q0FFOUM4QyxPQUFPOUMsS0FBSyxrQkFDWCw4REFBQ21IO2dEQUFFSixXQUFVOzBEQUF3QmpFLE9BQU85QyxLQUFLLENBQUMwRyxPQUFPOzs7Ozs7Ozs7Ozs7a0RBSTdELDhEQUFDTzt3Q0FBSUYsV0FBVTs7MERBQ2IsOERBQUN0SSx1REFBS0E7Z0RBQUM0SSxTQUFROzBEQUFROzs7Ozs7MERBQ3ZCLDhEQUFDN0ksdURBQUtBO2dEQUNKK0csSUFBRztnREFDRixHQUFHL0MsU0FBUyxRQUFRO2dEQUNyQnVFLFdBQVdqRSxPQUFPN0MsS0FBSyxHQUFHLG1CQUFtQjs7Ozs7OzRDQUU5QzZDLE9BQU83QyxLQUFLLGtCQUNYLDhEQUFDa0g7Z0RBQUVKLFdBQVU7MERBQXdCakUsT0FBTzdDLEtBQUssQ0FBQ3lHLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLL0QsOERBQUNPO2dDQUFJRixXQUFVOztrREFDYiw4REFBQ0U7d0NBQUlGLFdBQVU7OzBEQUNiLDhEQUFDdEksdURBQUtBO2dEQUFDNEksU0FBUTswREFBYzs7Ozs7OzBEQUM3Qiw4REFBQzdJLHVEQUFLQTtnREFDSitHLElBQUc7Z0RBQ0hnQyxNQUFLO2dEQUNKLEdBQUcvRSxTQUFTLGNBQWM7Z0RBQzNCdUUsV0FBV2pFLE9BQU81QyxXQUFXLEdBQUcsbUJBQW1COzs7Ozs7NENBRXBENEMsT0FBTzVDLFdBQVcsa0JBQ2pCLDhEQUFDaUg7Z0RBQUVKLFdBQVU7MERBQXdCakUsT0FBTzVDLFdBQVcsQ0FBQ3dHLE9BQU87Ozs7Ozs7Ozs7OztrREFJbkUsOERBQUNPO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ3RJLHVEQUFLQTtnREFBQzRJLFNBQVE7MERBQVM7Ozs7OzswREFDeEIsOERBQUMzSSx5REFBTUE7Z0RBQ0w4RixPQUFPNUIsTUFBTSxhQUFhO2dEQUMxQm1GLGVBQWUsQ0FBQ3ZELFFBQVU5QixTQUFTLFVBQVU4Qjs7a0VBRTdDLDhEQUFDM0YsZ0VBQWFBO3dEQUFDa0ksV0FBV2pFLE9BQU8zQyxNQUFNLEdBQUcsbUJBQW1CO2tFQUMzRCw0RUFBQ3JCLDhEQUFXQTs0REFBQ3dJLGFBQVk7Ozs7Ozs7Ozs7O2tFQUUzQiw4REFBQzNJLGdFQUFhQTs7MEVBQ1osOERBQUNDLDZEQUFVQTtnRUFBQzRGLE9BQU07MEVBQU87Ozs7OzswRUFDekIsOERBQUM1Riw2REFBVUE7Z0VBQUM0RixPQUFNOzBFQUFTOzs7Ozs7MEVBQzNCLDhEQUFDNUYsNkRBQVVBO2dFQUFDNEYsT0FBTTswRUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRDQUc3QjFCLE9BQU8zQyxNQUFNLGtCQUNaLDhEQUFDZ0g7Z0RBQUVKLFdBQVU7MERBQXdCakUsT0FBTzNDLE1BQU0sQ0FBQ3VHLE9BQU87Ozs7Ozs7Ozs7OztrREFJOUQsOERBQUNPO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ3RJLHVEQUFLQTtnREFBQzRJLFNBQVE7MERBQWdCOzs7Ozs7MERBQy9CLDhEQUFDN0ksdURBQUtBO2dEQUNKK0csSUFBRztnREFDSGdDLE1BQUs7Z0RBQ0osR0FBRy9FLFNBQVMsZ0JBQWdCO2dEQUM3QnVFLFdBQVdqRSxPQUFPbEMsYUFBYSxHQUFHLG1CQUFtQjs7Ozs7OzRDQUV0RGtDLE9BQU9sQyxhQUFhLGtCQUNuQiw4REFBQ3VHO2dEQUFFSixXQUFVOzBEQUF3QmpFLE9BQU9sQyxhQUFhLENBQUM4RixPQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS3ZFLDhEQUFDTztnQ0FBSUYsV0FBVTs7a0RBQ2IsOERBQUNFO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ3RJLHVEQUFLQTtnREFBQzRJLFNBQVE7MERBQWE7Ozs7OzswREFDNUIsOERBQUMzSSx5REFBTUE7Z0RBQ0w4RixPQUFPNUIsTUFBTSxpQkFBaUI7Z0RBQzlCbUYsZUFBZSxDQUFDdkQsUUFBVTlCLFNBQVMsY0FBYzhCOztrRUFFakQsOERBQUMzRixnRUFBYUE7d0RBQUNrSSxXQUFXakUsT0FBT3pDLFVBQVUsR0FBRyxtQkFBbUI7a0VBQy9ELDRFQUFDdkIsOERBQVdBOzREQUFDd0ksYUFBWTs7Ozs7Ozs7Ozs7a0VBRTNCLDhEQUFDM0ksZ0VBQWFBOzswRUFDWiw4REFBQ0MsNkRBQVVBO2dFQUFDNEYsT0FBTTswRUFBSzs7Ozs7OzBFQUN2Qiw4REFBQzVGLDZEQUFVQTtnRUFBQzRGLE9BQU07MEVBQUs7Ozs7OzswRUFDdkIsOERBQUM1Riw2REFBVUE7Z0VBQUM0RixPQUFNOzBFQUFLOzs7Ozs7MEVBQ3ZCLDhEQUFDNUYsNkRBQVVBO2dFQUFDNEYsT0FBTTswRUFBSzs7Ozs7OzBFQUN2Qiw4REFBQzVGLDZEQUFVQTtnRUFBQzRGLE9BQU07MEVBQU07Ozs7OzswRUFDeEIsOERBQUM1Riw2REFBVUE7Z0VBQUM0RixPQUFNOzBFQUFNOzs7Ozs7MEVBQ3hCLDhEQUFDNUYsNkRBQVVBO2dFQUFDNEYsT0FBTTswRUFBSzs7Ozs7OzBFQUN2Qiw4REFBQzVGLDZEQUFVQTtnRUFBQzRGLE9BQU07MEVBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0Q0FHMUIxQixPQUFPekMsVUFBVSxrQkFDaEIsOERBQUM4RztnREFBRUosV0FBVTswREFBd0JqRSxPQUFPekMsVUFBVSxDQUFDcUcsT0FBTzs7Ozs7Ozs7Ozs7O2tEQUlsRSw4REFBQ087d0NBQUlGLFdBQVU7OzBEQUNiLDhEQUFDdEksdURBQUtBO2dEQUFDNEksU0FBUTswREFBYzs7Ozs7OzBEQUM3Qiw4REFBQzdJLHVEQUFLQTtnREFDSitHLElBQUc7Z0RBQ0YsR0FBRy9DLFNBQVMsY0FBYztnREFDM0J1RSxXQUFXakUsT0FBT3hDLFdBQVcsR0FBRyxtQkFBbUI7Z0RBQ25EZ0gsYUFBWTs7Ozs7OzRDQUVieEUsT0FBT3hDLFdBQVcsa0JBQ2pCLDhEQUFDNkc7Z0RBQUVKLFdBQVU7MERBQXdCakUsT0FBT3hDLFdBQVcsQ0FBQ29HLE9BQU87Ozs7Ozs7Ozs7OztrREFJbkUsOERBQUNPO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ3RJLHVEQUFLQTtnREFBQzRJLFNBQVE7MERBQVc7Ozs7OzswREFDMUIsOERBQUM3SSx1REFBS0E7Z0RBQ0orRyxJQUFHO2dEQUNGLEdBQUcvQyxTQUFTLFdBQVc7Z0RBQ3hCdUUsV0FBV2pFLE9BQU92QyxRQUFRLEdBQUcsbUJBQW1COzs7Ozs7NENBRWpEdUMsT0FBT3ZDLFFBQVEsa0JBQ2QsOERBQUM0RztnREFBRUosV0FBVTswREFBd0JqRSxPQUFPdkMsUUFBUSxDQUFDbUcsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUtsRSw4REFBQ087Z0NBQUlGLFdBQVU7O2tEQUNiLDhEQUFDdEksdURBQUtBO3dDQUFDNEksU0FBUTtrREFBVTs7Ozs7O2tEQUN6Qiw4REFBQzdJLHVEQUFLQTt3Q0FDSitHLElBQUc7d0NBQ0YsR0FBRy9DLFNBQVMsVUFBVTt3Q0FDdkJ1RSxXQUFXakUsT0FBT3RDLE9BQU8sR0FBRyxtQkFBbUI7Ozs7OztvQ0FFaERzQyxPQUFPdEMsT0FBTyxrQkFDYiw4REFBQzJHO3dDQUFFSixXQUFVO2tEQUF3QmpFLE9BQU90QyxPQUFPLENBQUNrRyxPQUFPOzs7Ozs7Ozs7Ozs7MENBSS9ELDhEQUFDTztnQ0FBSUYsV0FBVTs7a0RBQ2IsOERBQUNFO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ3RJLHVEQUFLQTtnREFBQzRJLFNBQVE7MERBQXVCOzs7Ozs7MERBQ3RDLDhEQUFDN0ksdURBQUtBO2dEQUNKK0csSUFBRztnREFDRixHQUFHL0MsU0FBUyx1QkFBdUI7Z0RBQ3BDdUUsV0FBV2pFLE9BQU9yQyxvQkFBb0IsR0FBRyxtQkFBbUI7Ozs7Ozs0Q0FFN0RxQyxPQUFPckMsb0JBQW9CLGtCQUMxQiw4REFBQzBHO2dEQUFFSixXQUFVOzBEQUF3QmpFLE9BQU9yQyxvQkFBb0IsQ0FBQ2lHLE9BQU87Ozs7Ozs7Ozs7OztrREFJNUUsOERBQUNPO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ3RJLHVEQUFLQTtnREFBQzRJLFNBQVE7MERBQXdCOzs7Ozs7MERBQ3ZDLDhEQUFDN0ksdURBQUtBO2dEQUNKK0csSUFBRztnREFDRixHQUFHL0MsU0FBUyx3QkFBd0I7Z0RBQ3JDdUUsV0FBV2pFLE9BQU9wQyxxQkFBcUIsR0FBRyxtQkFBbUI7Ozs7Ozs0Q0FFOURvQyxPQUFPcEMscUJBQXFCLGtCQUMzQiw4REFBQ3lHO2dEQUFFSixXQUFVOzBEQUF3QmpFLE9BQU9wQyxxQkFBcUIsQ0FBQ2dHLE9BQU87Ozs7Ozs7Ozs7OztrREFJN0UsOERBQUNPO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ3RJLHVEQUFLQTtnREFBQzRJLFNBQVE7MERBQStCOzs7Ozs7MERBQzlDLDhEQUFDN0ksdURBQUtBO2dEQUNKK0csSUFBRztnREFDRixHQUFHL0MsU0FBUywrQkFBK0I7Z0RBQzVDdUUsV0FBV2pFLE9BQU9uQyw0QkFBNEIsR0FBRyxtQkFBbUI7Z0RBQ3BFMkcsYUFBWTs7Ozs7OzRDQUVieEUsT0FBT25DLDRCQUE0QixrQkFDbEMsOERBQUN3RztnREFBRUosV0FBVTswREFBd0JqRSxPQUFPbkMsNEJBQTRCLENBQUMrRixPQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS3RGLDhEQUFDTztnQ0FBSUYsV0FBVTs7a0RBQ2IsOERBQUNFO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ3RJLHVEQUFLQTtnREFBQzRJLFNBQVE7MERBQW9COzs7Ozs7MERBQ25DLDhEQUFDN0ksdURBQUtBO2dEQUNKK0csSUFBRztnREFDRixHQUFHL0MsU0FBUyxvQkFBb0I7Z0RBQ2pDdUUsV0FBV2pFLE9BQU83QixpQkFBaUIsR0FBRyxtQkFBbUI7Z0RBQ3pEcUcsYUFBWTs7Ozs7OzRDQUVieEUsT0FBTzdCLGlCQUFpQixrQkFDdkIsOERBQUNrRztnREFBRUosV0FBVTswREFBd0JqRSxPQUFPN0IsaUJBQWlCLENBQUN5RixPQUFPOzs7Ozs7Ozs7Ozs7a0RBSXpFLDhEQUFDTzt3Q0FBSUYsV0FBVTs7MERBQ2IsOERBQUN0SSx1REFBS0E7Z0RBQUM0SSxTQUFROzBEQUFZOzs7Ozs7MERBQzNCLDhEQUFDN0ksdURBQUtBO2dEQUNKK0csSUFBRztnREFDRixHQUFHL0MsU0FBUyxZQUFZO2dEQUN6QnVFLFdBQVdqRSxPQUFPNUIsU0FBUyxHQUFHLG1CQUFtQjtnREFDakRvRyxhQUFZOzs7Ozs7NENBRWJ4RSxPQUFPNUIsU0FBUyxrQkFDZiw4REFBQ2lHO2dEQUFFSixXQUFVOzBEQUF3QmpFLE9BQU81QixTQUFTLENBQUN3RixPQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBTWxFLENBQUNuRSw0QkFDQSw4REFBQzBFO2dDQUFJRixXQUFVOztrREFDYiw4REFBQ2lCO3dDQUFHakIsV0FBVTtrREFBc0I7Ozs7OztrREFDcEMsOERBQUNFO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ2hJLDZEQUFRQTtnREFDUHdHLElBQUc7Z0RBQ0gwQyxTQUFTckYsTUFBTTtnREFDZnNGLGlCQUFpQixDQUFDRCxVQUFZdkYsU0FBUyxvQkFBb0IsQ0FBQyxDQUFDdUY7Ozs7OzswREFFL0QsOERBQUN4Six1REFBS0E7Z0RBQUM0SSxTQUFRO2dEQUFtQk4sV0FBVTswREFBVTs7Ozs7Ozs7Ozs7O29DQUt2RCxDQUFDbkUsTUFBTSxxQ0FDTiw4REFBQ3FFO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ3RJLHVEQUFLQTtnREFBQzRJLFNBQVE7MERBQVc7Ozs7OzswREFDMUIsOERBQUM3SSx1REFBS0E7Z0RBQ0orRyxJQUFHO2dEQUNIZ0MsTUFBSztnREFDSixHQUFHL0UsU0FBUyxXQUFXO2dEQUN4QnVFLFdBQVdqRSxPQUFPeEIsUUFBUSxHQUFHLG1CQUFtQjtnREFDaERnRyxhQUFZOzs7Ozs7NENBRWJ4RSxPQUFPeEIsUUFBUSxrQkFDZCw4REFBQzZGO2dEQUFFSixXQUFVOzBEQUF3QmpFLE9BQU94QixRQUFRLENBQUNvRixPQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBT3RFLDhEQUFDTztnQ0FBSUYsV0FBVTs7a0RBQ2IsOERBQUN0SSx1REFBS0E7d0NBQUM0SSxTQUFRO2tEQUFrQjs7Ozs7O2tEQUNqQyw4REFBQ3JJLHNFQUFVQTt3Q0FDVG1KLFFBQU87d0NBQ1BDLFVBQVU7d0NBQ1ZDLFNBQVMsSUFBSSxPQUFPO3dDQUNwQkMsWUFBWTt3Q0FDWkMsY0FBYyxDQUFDQzs0Q0FDYixrREFBa0Q7NENBQ2xEcEQsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQm1EO3dDQUNqQzs7Ozs7O2tEQUVGLDhEQUFDckI7d0NBQUVKLFdBQVU7a0RBQWdDOzs7Ozs7Ozs7Ozs7MENBSy9DLDhEQUFDM0ksK0RBQVlBOztrREFDWCw4REFBQ0oseURBQU1BO3dDQUNMdUosTUFBSzt3Q0FDTDVCLFNBQVE7d0NBQ1I4QixTQUFTLElBQU0zRixRQUFRO3dDQUN2QjRGLFVBQVUzRjtrREFDWDs7Ozs7O2tEQUdELDhEQUFDL0QseURBQU1BO3dDQUFDdUosTUFBSzt3Q0FBU0csVUFBVTNGO2tEQUM3QkEsNkJBQ0M7OzhEQUNFLDhEQUFDNUMsbUdBQU9BO29EQUFDNEgsV0FBVTs7Ozs7O2dEQUNsQnhFLGFBQWEsZ0JBQWdCOzsyREFHaENBLGFBQWEsbUJBQW1COzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRaEQ7R0F4cUJnQmhCOztRQWdCVnpELHFEQUFPQTs7O0tBaEJHeUQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG9jdW1lbnRzXFxPUkFOR0VcXFBST0pFQ1RcXEEgSSBwcm9qZWN0c1xcc21zXFxmcm9udGVuZFxcY29tcG9uZW50c1xcbW9kYWxzXFxhZGQtc3R1ZGVudC1tb2RhbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcclxuXHJcbmltcG9ydCB0eXBlIFJlYWN0IGZyb20gXCJyZWFjdFwiXHJcblxyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCJcclxuaW1wb3J0IHsgdXNlRm9ybSB9IGZyb20gJ3JlYWN0LWhvb2stZm9ybSdcclxuaW1wb3J0IHsgem9kUmVzb2x2ZXIgfSBmcm9tICdAaG9va2Zvcm0vcmVzb2x2ZXJzL3pvZCdcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxyXG5pbXBvcnQge1xyXG4gIERpYWxvZyxcclxuICBEaWFsb2dDb250ZW50LFxyXG4gIERpYWxvZ0Rlc2NyaXB0aW9uLFxyXG4gIERpYWxvZ0Zvb3RlcixcclxuICBEaWFsb2dIZWFkZXIsXHJcbiAgRGlhbG9nVGl0bGUsXHJcbiAgRGlhbG9nVHJpZ2dlcixcclxufSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2RpYWxvZ1wiXHJcbmltcG9ydCB7IElucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiXHJcbmltcG9ydCB7IExhYmVsIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9sYWJlbFwiXHJcbmltcG9ydCB7IFNlbGVjdCwgU2VsZWN0Q29udGVudCwgU2VsZWN0SXRlbSwgU2VsZWN0VHJpZ2dlciwgU2VsZWN0VmFsdWUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NlbGVjdFwiXHJcbmltcG9ydCB7IENoZWNrYm94IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9jaGVja2JveFwiXHJcbmltcG9ydCB7IEZpbGVVcGxvYWQgfSBmcm9tIFwiQC9zcmMvY29tcG9uZW50cy91aS9maWxlLXVwbG9hZFwiXHJcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS91c2UtdG9hc3RcIlxyXG5pbXBvcnQgeyBjbGFzc2VzQXBpIH0gZnJvbSBcIkAvc3JjL2xpYi9hcGlcIlxyXG5pbXBvcnQgeyBMb2FkZXIyLCBQbHVzLCBSZWZyZXNoQ3cgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcclxuaW1wb3J0ICogYXMgeiBmcm9tIFwiem9kXCJcclxuXHJcblxyXG5cclxuLy8gQmFja2VuZC1jb21wYXRpYmxlIHN0dWRlbnQgc2NoZW1hXHJcbmNvbnN0IGJhY2tlbmRTdHVkZW50U2NoZW1hID0gei5vYmplY3Qoe1xyXG4gIGZpcnN0TmFtZTogei5zdHJpbmcoKS5taW4oMSwgJ0ZpcnN0IG5hbWUgaXMgcmVxdWlyZWQnKS5tYXgoMTAwLCAnRmlyc3QgbmFtZSBtdXN0IGJlIGxlc3MgdGhhbiAxMDAgY2hhcmFjdGVycycpLFxyXG4gIGxhc3ROYW1lOiB6LnN0cmluZygpLm1pbigxLCAnTGFzdCBuYW1lIGlzIHJlcXVpcmVkJykubWF4KDEwMCwgJ0xhc3QgbmFtZSBtdXN0IGJlIGxlc3MgdGhhbiAxMDAgY2hhcmFjdGVycycpLFxyXG4gIG1pZGRsZU5hbWU6IHouc3RyaW5nKCkubWF4KDEwMCwgJ01pZGRsZSBuYW1lIG11c3QgYmUgbGVzcyB0aGFuIDEwMCBjaGFyYWN0ZXJzJykub3B0aW9uYWwoKSxcclxuICBlbWFpbDogei5zdHJpbmcoKS5lbWFpbCgnUGxlYXNlIGVudGVyIGEgdmFsaWQgZW1haWwnKS5vcHRpb25hbCgpLFxyXG4gIHBob25lOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXHJcbiAgZGF0ZU9mQmlydGg6IHouc3RyaW5nKCkubWluKDEsICdEYXRlIG9mIGJpcnRoIGlzIHJlcXVpcmVkJyksXHJcbiAgZ2VuZGVyOiB6LmVudW0oWydtYWxlJywgJ2ZlbWFsZScsICdvdGhlciddKSxcclxuICBibG9vZEdyb3VwOiB6LmVudW0oWydBKycsICdBLScsICdCKycsICdCLScsICdBQisnLCAnQUItJywgJ08rJywgJ08tJ10pLm9wdGlvbmFsKCksXHJcbiAgbmF0aW9uYWxpdHk6IHouc3RyaW5nKCkubWF4KDEwMCwgJ05hdGlvbmFsaXR5IG11c3QgYmUgbGVzcyB0aGFuIDEwMCBjaGFyYWN0ZXJzJykub3B0aW9uYWwoKSxcclxuICByZWxpZ2lvbjogei5zdHJpbmcoKS5tYXgoMTAwLCAnUmVsaWdpb24gbXVzdCBiZSBsZXNzIHRoYW4gMTAwIGNoYXJhY3RlcnMnKS5vcHRpb25hbCgpLFxyXG4gIGFkZHJlc3M6IHouc3RyaW5nKCkubWF4KDUwMCwgJ0FkZHJlc3MgbXVzdCBiZSBsZXNzIHRoYW4gNTAwIGNoYXJhY3RlcnMnKS5vcHRpb25hbCgpLFxyXG4gIGVtZXJnZW5jeUNvbnRhY3ROYW1lOiB6LnN0cmluZygpLm1heCgyMDAsICdFbWVyZ2VuY3kgY29udGFjdCBuYW1lIG11c3QgYmUgbGVzcyB0aGFuIDIwMCBjaGFyYWN0ZXJzJykub3B0aW9uYWwoKSxcclxuICBlbWVyZ2VuY3lDb250YWN0UGhvbmU6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcclxuICBlbWVyZ2VuY3lDb250YWN0UmVsYXRpb25zaGlwOiB6LnN0cmluZygpLm1heCgxMDAsICdFbWVyZ2VuY3kgY29udGFjdCByZWxhdGlvbnNoaXAgbXVzdCBiZSBsZXNzIHRoYW4gMTAwIGNoYXJhY3RlcnMnKS5vcHRpb25hbCgpLFxyXG4gIGFkbWlzc2lvbkRhdGU6IHouc3RyaW5nKCkubWluKDEsICdBZG1pc3Npb24gZGF0ZSBpcyByZXF1aXJlZCcpLFxyXG4gIGFkbWlzc2lvbk51bWJlcjogei5zdHJpbmcoKS5tYXgoNTAsICdBZG1pc3Npb24gbnVtYmVyIG11c3QgYmUgbGVzcyB0aGFuIDUwIGNoYXJhY3RlcnMnKS5vcHRpb25hbCgpLFxyXG4gIGN1cnJlbnRDbGFzc0lkOiB6LnN0cmluZygpLm1pbigxLCAnUGxlYXNlIHNlbGVjdCBhIGNsYXNzJyksXHJcbiAgYWNhZGVtaWNZZWFySWQ6IHouc3RyaW5nKCkudXVpZCgnQWNhZGVtaWMgeWVhciBJRCBtdXN0IGJlIGEgdmFsaWQgVVVJRCcpLm9wdGlvbmFsKCksXHJcbiAgbWVkaWNhbENvbmRpdGlvbnM6IHouc3RyaW5nKCkubWF4KDEwMDAsICdNZWRpY2FsIGNvbmRpdGlvbnMgbXVzdCBiZSBsZXNzIHRoYW4gMTAwMCBjaGFyYWN0ZXJzJykub3B0aW9uYWwoKSxcclxuICBhbGxlcmdpZXM6IHouc3RyaW5nKCkubWF4KDEwMDAsICdBbGxlcmdpZXMgbXVzdCBiZSBsZXNzIHRoYW4gMTAwMCBjaGFyYWN0ZXJzJykub3B0aW9uYWwoKSxcclxuICBnZW5lcmF0ZVBhc3N3b3JkOiB6LmJvb2xlYW4oKS5kZWZhdWx0KHRydWUpLFxyXG4gIHBhc3N3b3JkOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXHJcbn0pXHJcblxyXG4vLyBJbnRlcmZhY2UgZm9yIGNsYXNzIGRhdGEgZnJvbSBBUEkgKG1hdGNoZXMgYmFja2VuZCByZXNwb25zZSlcclxuaW50ZXJmYWNlIENsYXNzRGF0YSB7XHJcbiAgaWQ6IHN0cmluZ1xyXG4gIHV1aWQ/OiBzdHJpbmcgIC8vIFVVSUQgZmllbGQgdGhhdCBiYWNrZW5kIGV4cGVjdHNcclxuICBuYW1lOiBzdHJpbmdcclxuICBzZWN0aW9uPzogc3RyaW5nXHJcbiAgY2FwYWNpdHk/OiBudW1iZXJcclxuICByb29tX251bWJlcj86IHN0cmluZ1xyXG4gIHN0YXR1czogc3RyaW5nXHJcbiAgY3JlYXRlZF9hdD86IHN0cmluZ1xyXG4gIGdyYWRlX2xldmVsPzogc3RyaW5nXHJcbiAgbGV2ZWxfbnVtYmVyPzogbnVtYmVyXHJcbiAgYWNhZGVtaWNfeWVhcj86IHN0cmluZ1xyXG4gIGNsYXNzX3RlYWNoZXJfbmFtZT86IHN0cmluZ1xyXG4gIHN0dWRlbnRfY291bnQ/OiBudW1iZXJcclxuICBzdWJqZWN0X2NvdW50PzogbnVtYmVyXHJcbn1cclxuXHJcbnR5cGUgQmFja2VuZFN0dWRlbnRGb3JtRGF0YSA9IHouaW5mZXI8dHlwZW9mIGJhY2tlbmRTdHVkZW50U2NoZW1hPlxyXG5cclxuXHJcblxyXG5pbnRlcmZhY2UgQWRkU3R1ZGVudE1vZGFsUHJvcHMge1xyXG4gIG9uQWRkPzogKG5ld1N0dWRlbnQ6IEJhY2tlbmRTdHVkZW50Rm9ybURhdGEpID0+IFByb21pc2U8dm9pZD5cclxuICBvblVwZGF0ZT86ICh1cGRhdGVkU3R1ZGVudDogYW55KSA9PiBQcm9taXNlPHZvaWQ+XHJcbiAgc3R1ZGVudD86IGFueSAvLyBTdHVkZW50IGRhdGEgZm9yIGVkaXQgbW9kZVxyXG4gIG1vZGU/OiAnYWRkJyB8ICdlZGl0J1xyXG4gIHRyaWdnZXI/OiBSZWFjdC5SZWFjdE5vZGVcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIEFkZFN0dWRlbnRNb2RhbCh7IG9uQWRkLCBvblVwZGF0ZSwgc3R1ZGVudCwgbW9kZSA9ICdhZGQnLCB0cmlnZ2VyIH06IEFkZFN0dWRlbnRNb2RhbFByb3BzKSB7XHJcbiAgY29uc3QgW29wZW4sIHNldE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgY29uc3QgW2lzU3VibWl0dGluZywgc2V0SXNTdWJtaXR0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gIGNvbnN0IFtjbGFzc2VzLCBzZXRDbGFzc2VzXSA9IHVzZVN0YXRlPENsYXNzRGF0YVtdPihbXSlcclxuICBjb25zdCBbbG9hZGluZ0NsYXNzZXMsIHNldExvYWRpbmdDbGFzc2VzXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gIGNvbnN0IFtnZW5lcmF0ZWRTdHVkZW50SWQsIHNldEdlbmVyYXRlZFN0dWRlbnRJZF0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKVxyXG5cclxuICBjb25zdCBpc0VkaXRNb2RlID0gbW9kZSA9PT0gJ2VkaXQnICYmIHN0dWRlbnRcclxuXHJcbiAgY29uc3Qge1xyXG4gICAgcmVnaXN0ZXIsXHJcbiAgICBoYW5kbGVTdWJtaXQsXHJcbiAgICBzZXRWYWx1ZSxcclxuICAgIHJlc2V0LFxyXG4gICAgd2F0Y2gsXHJcbiAgICBmb3JtU3RhdGU6IHsgZXJyb3JzIH0sXHJcbiAgfSA9IHVzZUZvcm08QmFja2VuZFN0dWRlbnRGb3JtRGF0YT4oe1xyXG4gICAgcmVzb2x2ZXI6IHpvZFJlc29sdmVyKGJhY2tlbmRTdHVkZW50U2NoZW1hKSxcclxuICAgIGRlZmF1bHRWYWx1ZXM6IHtcclxuICAgICAgZ2VuZGVyOiAnbWFsZScsXHJcbiAgICAgIGFkbWlzc2lvbkRhdGU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLFxyXG4gICAgICBnZW5lcmF0ZVBhc3N3b3JkOiB0cnVlLFxyXG4gICAgfSxcclxuICB9KVxyXG5cclxuICAvLyBGZXRjaCBjbGFzc2VzIGFuZCBnZW5lcmF0ZSBzdHVkZW50IElEIHByZXZpZXcgd2hlbiBtb2RhbCBvcGVuc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAob3Blbikge1xyXG4gICAgICBmZXRjaENsYXNzZXMoKVxyXG4gICAgICBpZiAoIWlzRWRpdE1vZGUpIHtcclxuICAgICAgICBnZW5lcmF0ZVN0dWRlbnRJZFByZXZpZXcoKVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSwgW29wZW4sIGlzRWRpdE1vZGVdKVxyXG5cclxuICAvLyBQb3B1bGF0ZSBmb3JtIHdpdGggc3R1ZGVudCBkYXRhIHdoZW4gaW4gZWRpdCBtb2RlXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChpc0VkaXRNb2RlICYmIHN0dWRlbnQgJiYgb3Blbikge1xyXG4gICAgICAvLyBDb252ZXJ0IHN0dWRlbnQgZGF0YSB0byBmb3JtIGZvcm1hdFxyXG4gICAgICBjb25zdCBmb3JtRGF0YSA9IHtcclxuICAgICAgICBmaXJzdE5hbWU6IHN0dWRlbnQuZmlyc3RfbmFtZSB8fCAnJyxcclxuICAgICAgICBsYXN0TmFtZTogc3R1ZGVudC5sYXN0X25hbWUgfHwgJycsXHJcbiAgICAgICAgbWlkZGxlTmFtZTogc3R1ZGVudC5taWRkbGVfbmFtZSB8fCAnJyxcclxuICAgICAgICBlbWFpbDogc3R1ZGVudC5lbWFpbCB8fCAnJyxcclxuICAgICAgICBwaG9uZTogc3R1ZGVudC5waG9uZSB8fCAnJyxcclxuICAgICAgICBkYXRlT2ZCaXJ0aDogc3R1ZGVudC5kYXRlX29mX2JpcnRoID8gc3R1ZGVudC5kYXRlX29mX2JpcnRoLnNwbGl0KCdUJylbMF0gOiAnJyxcclxuICAgICAgICBnZW5kZXI6IHN0dWRlbnQuZ2VuZGVyIHx8ICdtYWxlJyxcclxuICAgICAgICBibG9vZEdyb3VwOiBzdHVkZW50LmJsb29kX2dyb3VwIHx8ICcnLFxyXG4gICAgICAgIG5hdGlvbmFsaXR5OiBzdHVkZW50Lm5hdGlvbmFsaXR5IHx8ICcnLFxyXG4gICAgICAgIHJlbGlnaW9uOiBzdHVkZW50LnJlbGlnaW9uIHx8ICcnLFxyXG4gICAgICAgIGFkZHJlc3M6IHN0dWRlbnQuYWRkcmVzcyB8fCAnJyxcclxuICAgICAgICBlbWVyZ2VuY3lDb250YWN0TmFtZTogc3R1ZGVudC5lbWVyZ2VuY3lfY29udGFjdF9uYW1lIHx8ICcnLFxyXG4gICAgICAgIGVtZXJnZW5jeUNvbnRhY3RQaG9uZTogc3R1ZGVudC5lbWVyZ2VuY3lfY29udGFjdF9waG9uZSB8fCAnJyxcclxuICAgICAgICBlbWVyZ2VuY3lDb250YWN0UmVsYXRpb25zaGlwOiBzdHVkZW50LmVtZXJnZW5jeV9jb250YWN0X3JlbGF0aW9uIHx8ICcnLFxyXG4gICAgICAgIGFkbWlzc2lvbkRhdGU6IHN0dWRlbnQuYWRtaXNzaW9uX2RhdGUgPyBzdHVkZW50LmFkbWlzc2lvbl9kYXRlLnNwbGl0KCdUJylbMF0gOiAnJyxcclxuICAgICAgICBhZG1pc3Npb25OdW1iZXI6IHN0dWRlbnQuYWRtaXNzaW9uX251bWJlciB8fCAnJyxcclxuICAgICAgICBjdXJyZW50Q2xhc3NJZDogc3R1ZGVudC5jdXJyZW50X2NsYXNzX2lkIHx8IHN0dWRlbnQuY2xhc3NfaWQgfHwgJycsXHJcbiAgICAgICAgbWVkaWNhbENvbmRpdGlvbnM6IHN0dWRlbnQubWVkaWNhbF9jb25kaXRpb25zIHx8ICcnLFxyXG4gICAgICAgIGFsbGVyZ2llczogc3R1ZGVudC5hbGxlcmdpZXMgfHwgJycsXHJcbiAgICAgICAgZ2VuZXJhdGVQYXNzd29yZDogZmFsc2UsIC8vIERvbid0IGdlbmVyYXRlIG5ldyBwYXNzd29yZCBmb3IgZXhpc3Rpbmcgc3R1ZGVudHNcclxuICAgICAgICBwYXNzd29yZDogJycsXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFNldCBmb3JtIHZhbHVlc1xyXG4gICAgICBPYmplY3QuZW50cmllcyhmb3JtRGF0YSkuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7XHJcbiAgICAgICAgc2V0VmFsdWUoa2V5IGFzIGtleW9mIEJhY2tlbmRTdHVkZW50Rm9ybURhdGEsIHZhbHVlIGFzIGFueSlcclxuICAgICAgfSlcclxuICAgIH1cclxuICB9LCBbaXNFZGl0TW9kZSwgc3R1ZGVudCwgb3Blbiwgc2V0VmFsdWVdKVxyXG5cclxuICBjb25zdCBmZXRjaENsYXNzZXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRMb2FkaW5nQ2xhc3Nlcyh0cnVlKVxyXG5cclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjbGFzc2VzQXBpLmdldEFsbCh7XHJcbiAgICAgICAgc3RhdHVzOiAnYWN0aXZlJyxcclxuICAgICAgICBsaW1pdDogMTAwLFxyXG4gICAgICAgIHNvcnRfYnk6ICduYW1lJyxcclxuICAgICAgICBzb3J0X29yZGVyOiAnQVNDJ1xyXG4gICAgICB9KVxyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xyXG4gICAgICAgIC8vIEhhbmRsZSBkaWZmZXJlbnQgcG9zc2libGUgcmVzcG9uc2Ugc3RydWN0dXJlc1xyXG4gICAgICAgIGxldCBjbGFzc2VzQXJyYXkgPSBbXVxyXG4gICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5kYXRhIGFzIGFueVxyXG4gICAgICAgIGlmIChkYXRhLmNsYXNzZXMgJiYgQXJyYXkuaXNBcnJheShkYXRhLmNsYXNzZXMpKSB7XHJcbiAgICAgICAgICBjbGFzc2VzQXJyYXkgPSBkYXRhLmNsYXNzZXNcclxuICAgICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHtcclxuICAgICAgICAgIGNsYXNzZXNBcnJheSA9IHJlc3BvbnNlLmRhdGFcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKCdGaXJzdCBjbGFzcyBzdHJ1Y3R1cmU6JywgY2xhc3Nlc0FycmF5WzBdKVxyXG4gICAgICAgIGNvbnNvbGUubG9nKCdBdmFpbGFibGUgZmllbGRzOicsIE9iamVjdC5rZXlzKGNsYXNzZXNBcnJheVswXSB8fCB7fSkpXHJcbiAgICAgICAgY29uc29sZS5sb2coJ0lEIGZpZWxkOicsIGNsYXNzZXNBcnJheVswXT8uaWQsICdVVUlEIGZpZWxkOicsIGNsYXNzZXNBcnJheVswXT8udXVpZClcclxuICAgICAgICBzZXRDbGFzc2VzKGNsYXNzZXNBcnJheSlcclxuXHJcbiAgICAgICAgaWYgKGNsYXNzZXNBcnJheS5sZW5ndGggPT09IDApIHtcclxuICAgICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgICAgdGl0bGU6IFwiTm8gQ2xhc3NlcyBGb3VuZFwiLFxyXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJObyBhY3RpdmUgY2xhc3NlcyBhcmUgYXZhaWxhYmxlLiBQbGVhc2UgY29udGFjdCBhbiBhZG1pbmlzdHJhdG9yLlwiLFxyXG4gICAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgICAgICB9KVxyXG4gICAgICAgIH1cclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRDbGFzc2VzKFtdKVxyXG4gICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgIHRpdGxlOiBcIldhcm5pbmdcIixcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIkZhaWxlZCB0byBsb2FkIGNsYXNzZXMuIFBsZWFzZSByZWZyZXNoIGFuZCB0cnkgYWdhaW4uXCIsXHJcbiAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgICAgfSlcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgY2xhc3NlczonLCBlcnJvcilcclxuICAgICAgc2V0Q2xhc3NlcyhbXSlcclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiBcIkVycm9yXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiRmFpbGVkIHRvIGxvYWQgY2xhc3Nlcy4gUGxlYXNlIGNoZWNrIHlvdXIgY29ubmVjdGlvbi5cIixcclxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgIH0pXHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRMb2FkaW5nQ2xhc3NlcyhmYWxzZSlcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IGdlbmVyYXRlU3R1ZGVudElkUHJldmlldyA9ICgpID0+IHtcclxuICAgIGNvbnN0IGN1cnJlbnRZZWFyID0gbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpXHJcbiAgICAvLyBUaGlzIGlzIGp1c3QgYSBwcmV2aWV3IC0gdGhlIGFjdHVhbCBJRCB3aWxsIGJlIGdlbmVyYXRlZCBieSB0aGUgYmFja2VuZFxyXG4gICAgc2V0R2VuZXJhdGVkU3R1ZGVudElkKGBTVFUtJHtjdXJyZW50WWVhcn0jIyMjYClcclxuICB9XHJcblxyXG4gIGNvbnN0IG9uU3VibWl0ID0gYXN5bmMgKGRhdGE6IEJhY2tlbmRTdHVkZW50Rm9ybURhdGEpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIHNldElzU3VibWl0dGluZyh0cnVlKVxyXG4gICAgICBjb25zb2xlLmxvZygnRm9ybSBkYXRhIGJlaW5nIHN1Ym1pdHRlZDonLCBkYXRhKVxyXG4gICAgICBjb25zb2xlLmxvZygnU2VsZWN0ZWQgY2xhc3MgSUQ6JywgZGF0YS5jdXJyZW50Q2xhc3NJZCwgJ1R5cGU6JywgdHlwZW9mIGRhdGEuY3VycmVudENsYXNzSWQpXHJcblxyXG4gICAgICAvLyBWYWxpZGF0ZSByZXF1aXJlZCBmaWVsZHNcclxuICAgICAgaWYgKCFkYXRhLmN1cnJlbnRDbGFzc0lkKSB7XHJcbiAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgdGl0bGU6IFwiVmFsaWRhdGlvbiBFcnJvclwiLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb246IFwiUGxlYXNlIHNlbGVjdCBhIGNsYXNzIGZvciB0aGUgc3R1ZGVudC5cIixcclxuICAgICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcclxuICAgICAgICB9KVxyXG4gICAgICAgIHJldHVyblxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBGaW5kIHNlbGVjdGVkIGNsYXNzIGZvciBiZXR0ZXIgc3VjY2VzcyBtZXNzYWdlXHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkQ2xhc3MgPSBjbGFzc2VzLmZpbmQoY2xzID0+IGNscy5pZCA9PT0gZGF0YS5jdXJyZW50Q2xhc3NJZClcclxuXHJcbiAgICAgIGlmIChpc0VkaXRNb2RlICYmIG9uVXBkYXRlKSB7XHJcbiAgICAgICAgLy8gQ29udmVydCBmb3JtIGRhdGEgYmFjayB0byBzdHVkZW50IGZvcm1hdCBmb3IgdXBkYXRlXHJcbiAgICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IHtcclxuICAgICAgICAgIGlkOiBzdHVkZW50LmlkLFxyXG4gICAgICAgICAgZmlyc3RfbmFtZTogZGF0YS5maXJzdE5hbWUsXHJcbiAgICAgICAgICBsYXN0X25hbWU6IGRhdGEubGFzdE5hbWUsXHJcbiAgICAgICAgICBtaWRkbGVfbmFtZTogZGF0YS5taWRkbGVOYW1lLFxyXG4gICAgICAgICAgZW1haWw6IGRhdGEuZW1haWwsXHJcbiAgICAgICAgICBwaG9uZTogZGF0YS5waG9uZSxcclxuICAgICAgICAgIGRhdGVfb2ZfYmlydGg6IGRhdGEuZGF0ZU9mQmlydGgsXHJcbiAgICAgICAgICBnZW5kZXI6IGRhdGEuZ2VuZGVyLFxyXG4gICAgICAgICAgYmxvb2RfZ3JvdXA6IGRhdGEuYmxvb2RHcm91cCxcclxuICAgICAgICAgIG5hdGlvbmFsaXR5OiBkYXRhLm5hdGlvbmFsaXR5LFxyXG4gICAgICAgICAgcmVsaWdpb246IGRhdGEucmVsaWdpb24sXHJcbiAgICAgICAgICBhZGRyZXNzOiBkYXRhLmFkZHJlc3MsXHJcbiAgICAgICAgICBlbWVyZ2VuY3lfY29udGFjdF9uYW1lOiBkYXRhLmVtZXJnZW5jeUNvbnRhY3ROYW1lLFxyXG4gICAgICAgICAgZW1lcmdlbmN5X2NvbnRhY3RfcGhvbmU6IGRhdGEuZW1lcmdlbmN5Q29udGFjdFBob25lLFxyXG4gICAgICAgICAgZW1lcmdlbmN5X2NvbnRhY3RfcmVsYXRpb246IGRhdGEuZW1lcmdlbmN5Q29udGFjdFJlbGF0aW9uc2hpcCxcclxuICAgICAgICAgIGFkbWlzc2lvbl9kYXRlOiBkYXRhLmFkbWlzc2lvbkRhdGUsXHJcbiAgICAgICAgICBhZG1pc3Npb25fbnVtYmVyOiBkYXRhLmFkbWlzc2lvbk51bWJlcixcclxuICAgICAgICAgIGN1cnJlbnRfY2xhc3NfaWQ6IGRhdGEuY3VycmVudENsYXNzSWQsXHJcbiAgICAgICAgICBtZWRpY2FsX2NvbmRpdGlvbnM6IGRhdGEubWVkaWNhbENvbmRpdGlvbnMsXHJcbiAgICAgICAgICBhbGxlcmdpZXM6IGRhdGEuYWxsZXJnaWVzLFxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coJ1VwZGF0ZSBkYXRhIGJlaW5nIHNlbnQgdG8gYmFja2VuZDonLCB1cGRhdGVEYXRhKVxyXG4gICAgICAgIGF3YWl0IG9uVXBkYXRlKHVwZGF0ZURhdGEpXHJcblxyXG4gICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgIHRpdGxlOiBcIlN0dWRlbnQgVXBkYXRlZCBTdWNjZXNzZnVsbHkhIOKchVwiLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb246IGAke2RhdGEuZmlyc3ROYW1lfSAke2RhdGEubGFzdE5hbWV9J3MgaW5mb3JtYXRpb24gaGFzIGJlZW4gdXBkYXRlZC5gLFxyXG4gICAgICAgIH0pXHJcbiAgICAgIH0gZWxzZSBpZiAob25BZGQpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnRm9ybSBkYXRhIGJlaW5nIHNlbnQgdG8gYmFja2VuZDonLCBkYXRhKVxyXG4gICAgICAgIGF3YWl0IG9uQWRkKGRhdGEpXHJcblxyXG4gICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgIHRpdGxlOiBcIlN0dWRlbnQgQWRkZWQgU3VjY2Vzc2Z1bGx5ISDwn46JXCIsXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogYCR7ZGF0YS5maXJzdE5hbWV9ICR7ZGF0YS5sYXN0TmFtZX0gaGFzIGJlZW4gZW5yb2xsZWQgaW4gJHtzZWxlY3RlZENsYXNzPy5uYW1lIHx8ICd0aGUgc2VsZWN0ZWQgY2xhc3MnfS5gLFxyXG4gICAgICAgIH0pXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFJlc2V0IGZvcm0gYW5kIGNsb3NlIG1vZGFsXHJcbiAgICAgIHJlc2V0KClcclxuICAgICAgc2V0T3BlbihmYWxzZSlcclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWRkaW5nIHN0dWRlbnQ6JywgZXJyb3IpXHJcblxyXG4gICAgICAvLyBIYW5kbGUgdmFsaWRhdGlvbiBlcnJvcnMgZnJvbSBiYWNrZW5kXHJcbiAgICAgIGlmIChlcnJvcj8ucmVzcG9uc2U/LmRhdGE/LmVycm9ycyAmJiBBcnJheS5pc0FycmF5KGVycm9yLnJlc3BvbnNlLmRhdGEuZXJyb3JzKSkge1xyXG4gICAgICAgIGNvbnN0IHZhbGlkYXRpb25FcnJvcnMgPSBlcnJvci5yZXNwb25zZS5kYXRhLmVycm9yc1xyXG4gICAgICAgIGNvbnN0IGNsYXNzRXJyb3IgPSB2YWxpZGF0aW9uRXJyb3JzLmZpbmQoKGVycjogYW55KSA9PiBlcnIuZmllbGQgPT09ICdjdXJyZW50Q2xhc3NJZCcpXHJcblxyXG4gICAgICAgIGlmIChjbGFzc0Vycm9yKSB7XHJcbiAgICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICAgIHRpdGxlOiBcIkNsYXNzIFNlbGVjdGlvbiBFcnJvclwiLFxyXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJUaGUgc2VsZWN0ZWQgY2xhc3MgaXMgaW52YWxpZC4gUGxlYXNlIHNlbGVjdCBhIGRpZmZlcmVudCBjbGFzcyBhbmQgdHJ5IGFnYWluLlwiLFxyXG4gICAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgICAgICB9KVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAvLyBTaG93IGZpcnN0IHZhbGlkYXRpb24gZXJyb3JcclxuICAgICAgICAgIGNvbnN0IGZpcnN0RXJyb3IgPSB2YWxpZGF0aW9uRXJyb3JzWzBdXHJcbiAgICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICAgIHRpdGxlOiBcIlZhbGlkYXRpb24gRXJyb3JcIixcclxuICAgICAgICAgICAgZGVzY3JpcHRpb246IGAke2ZpcnN0RXJyb3IubWVzc2FnZSB8fCAnUGxlYXNlIGNoZWNrIHlvdXIgaW5wdXQgYW5kIHRyeSBhZ2Fpbi4nfWAsXHJcbiAgICAgICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcclxuICAgICAgICAgIH0pXHJcbiAgICAgICAgfVxyXG4gICAgICB9IGVsc2UgaWYgKGVycm9yPy5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSkge1xyXG4gICAgICAgIC8vIEhhbmRsZSBnZW5lcmFsIGVycm9yIG1lc3NhZ2VzXHJcbiAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgdGl0bGU6IFwiRXJyb3JcIixcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBlcnJvci5yZXNwb25zZS5kYXRhLm1lc3NhZ2UsXHJcbiAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgICAgfSlcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICAvLyBIYW5kbGUgdW5rbm93biBlcnJvcnNcclxuICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICB0aXRsZTogXCJFcnJvclwiLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb246IGBGYWlsZWQgdG8gJHtpc0VkaXRNb2RlID8gJ3VwZGF0ZScgOiAnYWRkJ30gc3R1ZGVudC4gUGxlYXNlIHRyeSBhZ2Fpbi5gLFxyXG4gICAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxyXG4gICAgICAgIH0pXHJcbiAgICAgIH1cclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzU3VibWl0dGluZyhmYWxzZSlcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vIFJlc2V0IGZvcm0gd2hlbiBtb2RhbCBpcyBjbG9zZWRcclxuICBjb25zdCBoYW5kbGVNb2RhbENsb3NlID0gKGlzT3BlbjogYm9vbGVhbikgPT4ge1xyXG4gICAgc2V0T3Blbihpc09wZW4pXHJcbiAgICBpZiAoIWlzT3Blbikge1xyXG4gICAgICByZXNldCgpXHJcbiAgICAgIHNldEdlbmVyYXRlZFN0dWRlbnRJZCgnJylcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8RGlhbG9nIG9wZW49e29wZW59IG9uT3BlbkNoYW5nZT17aGFuZGxlTW9kYWxDbG9zZX0+XHJcbiAgICAgIDxEaWFsb2dUcmlnZ2VyIGFzQ2hpbGQ+XHJcbiAgICAgICAge3RyaWdnZXIgfHwgKFxyXG4gICAgICAgICAgPEJ1dHRvbj5cclxuICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgQWRkIFN0dWRlbnRcclxuICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvRGlhbG9nVHJpZ2dlcj5cclxuICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwibWF4LWgtWzkwdmhdIG1heC13LVs4MDBweF0gb3ZlcmZsb3cteS1hdXRvXCI+XHJcbiAgICAgICAgPERpYWxvZ0hlYWRlcj5cclxuICAgICAgICAgIDxEaWFsb2dUaXRsZT57aXNFZGl0TW9kZSA/ICdFZGl0IFN0dWRlbnQnIDogJ0FkZCBOZXcgU3R1ZGVudCd9PC9EaWFsb2dUaXRsZT5cclxuICAgICAgICAgIDxEaWFsb2dEZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAge2lzRWRpdE1vZGVcclxuICAgICAgICAgICAgICA/ICdVcGRhdGUgdGhlIHN0dWRlbnQgaW5mb3JtYXRpb24gYmVsb3cuIENsaWNrIHNhdmUgd2hlbiB5b3VcXCdyZSBkb25lLidcclxuICAgICAgICAgICAgICA6ICdFbnRlciB0aGUgc3R1ZGVudCBpbmZvcm1hdGlvbiBiZWxvdy4gQ2xpY2sgc2F2ZSB3aGVuIHlvdVxcJ3JlIGRvbmUuJ1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICA8L0RpYWxvZ0Rlc2NyaXB0aW9uPlxyXG4gICAgICAgIDwvRGlhbG9nSGVhZGVyPlxyXG5cclxuICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0KG9uU3VibWl0KX0gY2xhc3NOYW1lPVwic3BhY2UteS00IG10LTRcIj5cclxuXHJcbiAgICAgICAgICB7LyogU3R1ZGVudCBJRCBQcmV2aWV3IC0gb25seSBzaG93IGluIGFkZCBtb2RlICovfVxyXG4gICAgICAgICAgeyFpc0VkaXRNb2RlICYmIChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwIGJvcmRlciBib3JkZXItYmx1ZS0yMDAgcm91bmRlZC1sZyBwLTRcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmx1ZS01MDAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS04MDBcIj5TdHVkZW50IElEIFByZXZpZXc8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNjAwIG10LTFcIj5cclxuICAgICAgICAgICAgICAgIFN0dWRlbnQgSUQgd2lsbCBiZSBhdXRvbWF0aWNhbGx5IGdlbmVyYXRlZDogPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tb25vIGZvbnQtc2VtaWJvbGRcIj57Z2VuZXJhdGVkU3R1ZGVudElkfTwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtNTAwIG10LTFcIj5cclxuICAgICAgICAgICAgICAgIFRoZSBhY3R1YWwgSUQgd2lsbCBiZSBhc3NpZ25lZCB3aGVuIHRoZSBzdHVkZW50IGlzIGNyZWF0ZWRcclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICB7LyogQ3VycmVudCBTdHVkZW50IElEIC0gb25seSBzaG93IGluIGVkaXQgbW9kZSAqL31cclxuICAgICAgICAgIHtpc0VkaXRNb2RlICYmIHN0dWRlbnQgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIHJvdW5kZWQtbGcgcC00XCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWdyZWVuLTUwMCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmVlbi04MDBcIj5DdXJyZW50IFN0dWRlbnQgSUQ8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyZWVuLTYwMCBtdC0xXCI+XHJcbiAgICAgICAgICAgICAgICBTdHVkZW50IElEOiA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1vbm8gZm9udC1zZW1pYm9sZFwiPntzdHVkZW50LnN0dWRlbnRfaWR9PC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICB7c3R1ZGVudC5hZG1pc3Npb25fbnVtYmVyICYmIChcclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmVlbi01MDAgbXQtMVwiPlxyXG4gICAgICAgICAgICAgICAgICBBZG1pc3Npb24gTnVtYmVyOiB7c3R1ZGVudC5hZG1pc3Npb25fbnVtYmVyfVxyXG4gICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKX1cclxuXHJcblxyXG5cclxuXHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJhZG1pc3Npb25OdW1iZXJcIj5BZG1pc3Npb24gTnVtYmVyIChPcHRpb25hbCk8L0xhYmVsPlxyXG4gICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgaWQ9XCJhZG1pc3Npb25OdW1iZXJcIlxyXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdhZG1pc3Npb25OdW1iZXInKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17ZXJyb3JzLmFkbWlzc2lvbk51bWJlciA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ31cclxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTGVhdmUgZW1wdHkgdG8gYXV0by1nZW5lcmF0ZVwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICB7ZXJyb3JzLmFkbWlzc2lvbk51bWJlciAmJiAoXHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMuYWRtaXNzaW9uTnVtYmVyLm1lc3NhZ2V9PC9wPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjdXJyZW50Q2xhc3NJZFwiPkNsYXNzICo8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcclxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17ZmV0Y2hDbGFzc2VzfVxyXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ0NsYXNzZXN9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNiBweC0yIHRleHQteHNcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT17YGgtMyB3LTMgbXItMSAke2xvYWRpbmdDbGFzc2VzID8gJ2FuaW1hdGUtc3BpbicgOiAnJ31gfSAvPlxyXG4gICAgICAgICAgICAgICAgICBSZWZyZXNoXHJcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8c2VsZWN0XHJcbiAgICAgICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ2N1cnJlbnRDbGFzc0lkJyl9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgJHtlcnJvcnMuY3VycmVudENsYXNzSWQgPyAnYm9yZGVyLXJlZC01MDAnIDogJyd9YH1cclxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nQ2xhc3Nlc31cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+XHJcbiAgICAgICAgICAgICAgICAgIHtsb2FkaW5nQ2xhc3NlcyA/IFwiTG9hZGluZyBjbGFzc2VzLi4uXCIgOiBcIlNlbGVjdCBjbGFzc1wifVxyXG4gICAgICAgICAgICAgICAgPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICB7Y2xhc3Nlcy5tYXAoKGNscykgPT4gKFxyXG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17Y2xzLmlkfSB2YWx1ZT17Y2xzLmlkfT5cclxuICAgICAgICAgICAgICAgICAgICB7Y2xzLmdyYWRlX2xldmVsID8gYCR7Y2xzLm5hbWV9ICgke2Nscy5ncmFkZV9sZXZlbH0pYCA6IGNscy5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxyXG5cclxuICAgICAgICAgICAgICB7ZXJyb3JzLmN1cnJlbnRDbGFzc0lkICYmIChcclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5jdXJyZW50Q2xhc3NJZC5tZXNzYWdlfTwvcD5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIHtjbGFzc2VzLmxlbmd0aCA9PT0gMCAmJiAhbG9hZGluZ0NsYXNzZXMgJiYgKFxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWFtYmVyLTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICBObyBjbGFzc2VzIGZvdW5kLiBQbGVhc2UgY29udGFjdCBhbiBhZG1pbmlzdHJhdG9yIHRvIGNyZWF0ZSBjbGFzc2VzIGZpcnN0LlxyXG4gICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0zIGdhcC00XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJmaXJzdE5hbWVcIj5GaXJzdCBOYW1lICo8L0xhYmVsPlxyXG4gICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgaWQ9XCJmaXJzdE5hbWVcIlxyXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdmaXJzdE5hbWUnKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17ZXJyb3JzLmZpcnN0TmFtZSA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ31cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIHtlcnJvcnMuZmlyc3ROYW1lICYmIChcclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5maXJzdE5hbWUubWVzc2FnZX08L3A+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwibWlkZGxlTmFtZVwiPk1pZGRsZSBOYW1lPC9MYWJlbD5cclxuICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgIGlkPVwibWlkZGxlTmFtZVwiXHJcbiAgICAgICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ21pZGRsZU5hbWUnKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17ZXJyb3JzLm1pZGRsZU5hbWUgPyAnYm9yZGVyLXJlZC01MDAnIDogJyd9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICB7ZXJyb3JzLm1pZGRsZU5hbWUgJiYgKFxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLm1pZGRsZU5hbWUubWVzc2FnZX08L3A+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwibGFzdE5hbWVcIj5MYXN0IE5hbWUgKjwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICBpZD1cImxhc3ROYW1lXCJcclxuICAgICAgICAgICAgICAgIHsuLi5yZWdpc3RlcignbGFzdE5hbWUnKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17ZXJyb3JzLmxhc3ROYW1lID8gJ2JvcmRlci1yZWQtNTAwJyA6ICcnfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAge2Vycm9ycy5sYXN0TmFtZSAmJiAoXHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMubGFzdE5hbWUubWVzc2FnZX08L3A+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImVtYWlsXCI+RW1haWwgKE9wdGlvbmFsKTwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICBpZD1cImVtYWlsXCJcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXHJcbiAgICAgICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ2VtYWlsJyl9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Vycm9ycy5lbWFpbCA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ31cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIHtlcnJvcnMuZW1haWwgJiYgKFxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmVtYWlsLm1lc3NhZ2V9PC9wPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInBob25lXCI+UGhvbmUgKE9wdGlvbmFsKTwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICBpZD1cInBob25lXCJcclxuICAgICAgICAgICAgICAgIHsuLi5yZWdpc3RlcigncGhvbmUnKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17ZXJyb3JzLnBob25lID8gJ2JvcmRlci1yZWQtNTAwJyA6ICcnfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAge2Vycm9ycy5waG9uZSAmJiAoXHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMucGhvbmUubWVzc2FnZX08L3A+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTMgZ2FwLTRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImRhdGVPZkJpcnRoXCI+RGF0ZSBvZiBCaXJ0aCAqPC9MYWJlbD5cclxuICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgIGlkPVwiZGF0ZU9mQmlydGhcIlxyXG4gICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxyXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdkYXRlT2ZCaXJ0aCcpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtlcnJvcnMuZGF0ZU9mQmlydGggPyAnYm9yZGVyLXJlZC01MDAnIDogJyd9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICB7ZXJyb3JzLmRhdGVPZkJpcnRoICYmIChcclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5kYXRlT2ZCaXJ0aC5tZXNzYWdlfTwvcD5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJnZW5kZXJcIj5HZW5kZXIgKjwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgPFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e3dhdGNoKCdnZW5kZXInKSB8fCAnbWFsZSd9XHJcbiAgICAgICAgICAgICAgICBvblZhbHVlQ2hhbmdlPXsodmFsdWUpID0+IHNldFZhbHVlKCdnZW5kZXInLCB2YWx1ZSBhcyBhbnkpfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyIGNsYXNzTmFtZT17ZXJyb3JzLmdlbmRlciA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ30+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSBwbGFjZWhvbGRlcj1cIlNlbGVjdCBnZW5kZXJcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwibWFsZVwiPk1hbGU8L1NlbGVjdEl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiZmVtYWxlXCI+RmVtYWxlPC9TZWxlY3RJdGVtPlxyXG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cIm90aGVyXCI+T3RoZXI8L1NlbGVjdEl0ZW0+XHJcbiAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgPC9TZWxlY3Q+XHJcbiAgICAgICAgICAgICAge2Vycm9ycy5nZW5kZXIgJiYgKFxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmdlbmRlci5tZXNzYWdlfTwvcD5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJhZG1pc3Npb25EYXRlXCI+QWRtaXNzaW9uIERhdGUgKjwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICBpZD1cImFkbWlzc2lvbkRhdGVcIlxyXG4gICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxyXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdhZG1pc3Npb25EYXRlJyl9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Vycm9ycy5hZG1pc3Npb25EYXRlID8gJ2JvcmRlci1yZWQtNTAwJyA6ICcnfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAge2Vycm9ycy5hZG1pc3Npb25EYXRlICYmIChcclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5hZG1pc3Npb25EYXRlLm1lc3NhZ2V9PC9wPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0zIGdhcC00XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJibG9vZEdyb3VwXCI+Qmxvb2QgR3JvdXA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgIDxTZWxlY3RcclxuICAgICAgICAgICAgICAgIHZhbHVlPXt3YXRjaCgnYmxvb2RHcm91cCcpIHx8ICcnfVxyXG4gICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiBzZXRWYWx1ZSgnYmxvb2RHcm91cCcsIHZhbHVlIGFzIGFueSl9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgY2xhc3NOYW1lPXtlcnJvcnMuYmxvb2RHcm91cCA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ30+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSBwbGFjZWhvbGRlcj1cIlNlbGVjdCBibG9vZCBncm91cFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJBK1wiPkErPC9TZWxlY3RJdGVtPlxyXG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cIkEtXCI+QS08L1NlbGVjdEl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiQitcIj5CKzwvU2VsZWN0SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJCLVwiPkItPC9TZWxlY3RJdGVtPlxyXG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cIkFCK1wiPkFCKzwvU2VsZWN0SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJBQi1cIj5BQi08L1NlbGVjdEl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiTytcIj5PKzwvU2VsZWN0SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJPLVwiPk8tPC9TZWxlY3RJdGVtPlxyXG4gICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxyXG4gICAgICAgICAgICAgIDwvU2VsZWN0PlxyXG4gICAgICAgICAgICAgIHtlcnJvcnMuYmxvb2RHcm91cCAmJiAoXHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMuYmxvb2RHcm91cC5tZXNzYWdlfTwvcD5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJuYXRpb25hbGl0eVwiPk5hdGlvbmFsaXR5PC9MYWJlbD5cclxuICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgIGlkPVwibmF0aW9uYWxpdHlcIlxyXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCduYXRpb25hbGl0eScpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtlcnJvcnMubmF0aW9uYWxpdHkgPyAnYm9yZGVyLXJlZC01MDAnIDogJyd9XHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIEluZGlhblwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICB7ZXJyb3JzLm5hdGlvbmFsaXR5ICYmIChcclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5uYXRpb25hbGl0eS5tZXNzYWdlfTwvcD5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJyZWxpZ2lvblwiPlJlbGlnaW9uPC9MYWJlbD5cclxuICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgIGlkPVwicmVsaWdpb25cIlxyXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdyZWxpZ2lvbicpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtlcnJvcnMucmVsaWdpb24gPyAnYm9yZGVyLXJlZC01MDAnIDogJyd9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICB7ZXJyb3JzLnJlbGlnaW9uICYmIChcclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5yZWxpZ2lvbi5tZXNzYWdlfTwvcD5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiYWRkcmVzc1wiPkFkZHJlc3MgKE9wdGlvbmFsKTwvTGFiZWw+XHJcbiAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgIGlkPVwiYWRkcmVzc1wiXHJcbiAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdhZGRyZXNzJyl9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtlcnJvcnMuYWRkcmVzcyA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ31cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAge2Vycm9ycy5hZGRyZXNzICYmIChcclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMuYWRkcmVzcy5tZXNzYWdlfTwvcD5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMyBnYXAtNFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZW1lcmdlbmN5Q29udGFjdE5hbWVcIj5FbWVyZ2VuY3kgQ29udGFjdCBOYW1lPC9MYWJlbD5cclxuICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgIGlkPVwiZW1lcmdlbmN5Q29udGFjdE5hbWVcIlxyXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdlbWVyZ2VuY3lDb250YWN0TmFtZScpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtlcnJvcnMuZW1lcmdlbmN5Q29udGFjdE5hbWUgPyAnYm9yZGVyLXJlZC01MDAnIDogJyd9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICB7ZXJyb3JzLmVtZXJnZW5jeUNvbnRhY3ROYW1lICYmIChcclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5lbWVyZ2VuY3lDb250YWN0TmFtZS5tZXNzYWdlfTwvcD5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJlbWVyZ2VuY3lDb250YWN0UGhvbmVcIj5FbWVyZ2VuY3kgQ29udGFjdCBQaG9uZTwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICBpZD1cImVtZXJnZW5jeUNvbnRhY3RQaG9uZVwiXHJcbiAgICAgICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ2VtZXJnZW5jeUNvbnRhY3RQaG9uZScpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtlcnJvcnMuZW1lcmdlbmN5Q29udGFjdFBob25lID8gJ2JvcmRlci1yZWQtNTAwJyA6ICcnfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAge2Vycm9ycy5lbWVyZ2VuY3lDb250YWN0UGhvbmUgJiYgKFxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmVtZXJnZW5jeUNvbnRhY3RQaG9uZS5tZXNzYWdlfTwvcD5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJlbWVyZ2VuY3lDb250YWN0UmVsYXRpb25zaGlwXCI+UmVsYXRpb25zaGlwPC9MYWJlbD5cclxuICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgIGlkPVwiZW1lcmdlbmN5Q29udGFjdFJlbGF0aW9uc2hpcFwiXHJcbiAgICAgICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ2VtZXJnZW5jeUNvbnRhY3RSZWxhdGlvbnNoaXAnKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17ZXJyb3JzLmVtZXJnZW5jeUNvbnRhY3RSZWxhdGlvbnNoaXAgPyAnYm9yZGVyLXJlZC01MDAnIDogJyd9XHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIEZhdGhlciwgTW90aGVyLCBHdWFyZGlhblwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICB7ZXJyb3JzLmVtZXJnZW5jeUNvbnRhY3RSZWxhdGlvbnNoaXAgJiYgKFxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmVtZXJnZW5jeUNvbnRhY3RSZWxhdGlvbnNoaXAubWVzc2FnZX08L3A+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cIm1lZGljYWxDb25kaXRpb25zXCI+TWVkaWNhbCBDb25kaXRpb25zPC9MYWJlbD5cclxuICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgIGlkPVwibWVkaWNhbENvbmRpdGlvbnNcIlxyXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdtZWRpY2FsQ29uZGl0aW9ucycpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtlcnJvcnMubWVkaWNhbENvbmRpdGlvbnMgPyAnYm9yZGVyLXJlZC01MDAnIDogJyd9XHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkFueSBtZWRpY2FsIGNvbmRpdGlvbnNcIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAge2Vycm9ycy5tZWRpY2FsQ29uZGl0aW9ucyAmJiAoXHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMubWVkaWNhbENvbmRpdGlvbnMubWVzc2FnZX08L3A+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiYWxsZXJnaWVzXCI+QWxsZXJnaWVzPC9MYWJlbD5cclxuICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgIGlkPVwiYWxsZXJnaWVzXCJcclxuICAgICAgICAgICAgICAgIHsuLi5yZWdpc3RlcignYWxsZXJnaWVzJyl9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Vycm9ycy5hbGxlcmdpZXMgPyAnYm9yZGVyLXJlZC01MDAnIDogJyd9XHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkFueSBrbm93biBhbGxlcmdpZXNcIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAge2Vycm9ycy5hbGxlcmdpZXMgJiYgKFxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmFsbGVyZ2llcy5tZXNzYWdlfTwvcD5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBQYXNzd29yZCBTZXR0aW5ncyAtIG9ubHkgc2hvdyBpbiBhZGQgbW9kZSAqL31cclxuICAgICAgICAgIHshaXNFZGl0TW9kZSAmJiAoXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00IGJvcmRlci10IHB0LTRcIj5cclxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlBhc3N3b3JkIFNldHRpbmdzPC9oND5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxyXG4gICAgICAgICAgICAgICAgPENoZWNrYm94XHJcbiAgICAgICAgICAgICAgICAgIGlkPVwiZ2VuZXJhdGVQYXNzd29yZFwiXHJcbiAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3dhdGNoKCdnZW5lcmF0ZVBhc3N3b3JkJyl9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17KGNoZWNrZWQpID0+IHNldFZhbHVlKCdnZW5lcmF0ZVBhc3N3b3JkJywgISFjaGVja2VkKX1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImdlbmVyYXRlUGFzc3dvcmRcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgIEdlbmVyYXRlIHBhc3N3b3JkIGF1dG9tYXRpY2FsbHkgKHJlY29tbWVuZGVkKVxyXG4gICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgeyF3YXRjaCgnZ2VuZXJhdGVQYXNzd29yZCcpICYmIChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwicGFzc3dvcmRcIj5DdXN0b20gUGFzc3dvcmQ8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICBpZD1cInBhc3N3b3JkXCJcclxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwicGFzc3dvcmRcIlxyXG4gICAgICAgICAgICAgICAgICAgIHsuLi5yZWdpc3RlcigncGFzc3dvcmQnKX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Vycm9ycy5wYXNzd29yZCA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ31cclxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGN1c3RvbSBwYXNzd29yZCAobWluIDggY2hhcmFjdGVycylcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICB7ZXJyb3JzLnBhc3N3b3JkICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMucGFzc3dvcmQubWVzc2FnZX08L3A+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJwcm9maWxlX3BpY3R1cmVcIj5Qcm9maWxlIFBpY3R1cmUgKE9wdGlvbmFsKTwvTGFiZWw+XHJcbiAgICAgICAgICAgIDxGaWxlVXBsb2FkXHJcbiAgICAgICAgICAgICAgYWNjZXB0PVwiaW1hZ2UvKlwiXHJcbiAgICAgICAgICAgICAgbWF4RmlsZXM9ezF9XHJcbiAgICAgICAgICAgICAgbWF4U2l6ZT17NSAqIDEwMjQgKiAxMDI0fSAvLyA1TUJcclxuICAgICAgICAgICAgICBhdXRvVXBsb2FkPXtmYWxzZX1cclxuICAgICAgICAgICAgICBvbkZpbGVTZWxlY3Q9eyhmaWxlcykgPT4ge1xyXG4gICAgICAgICAgICAgICAgLy8gSGFuZGxlIGZpbGUgc2VsZWN0aW9uIGZvciBmdXR1cmUgaW1wbGVtZW50YXRpb25cclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdTZWxlY3RlZCBmaWxlczonLCBmaWxlcylcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgIFVwbG9hZCBhIHByb2ZpbGUgcGljdHVyZSBmb3IgdGhlIHN0dWRlbnQgKG1heCA1TUIpXHJcbiAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIDxEaWFsb2dGb290ZXI+XHJcbiAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0T3BlbihmYWxzZSl9XHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzU3VibWl0dGluZ31cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIENhbmNlbFxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPEJ1dHRvbiB0eXBlPVwic3VibWl0XCIgZGlzYWJsZWQ9e2lzU3VibWl0dGluZ30+XHJcbiAgICAgICAgICAgICAge2lzU3VibWl0dGluZyA/IChcclxuICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNCBhbmltYXRlLXNwaW5cIiAvPlxyXG4gICAgICAgICAgICAgICAgICB7aXNFZGl0TW9kZSA/ICdVcGRhdGluZy4uLicgOiAnQWRkaW5nLi4uJ31cclxuICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICBpc0VkaXRNb2RlID8gJ1VwZGF0ZSBTdHVkZW50JyA6ICdBZGQgU3R1ZGVudCdcclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgIDwvRGlhbG9nRm9vdGVyPlxyXG4gICAgICAgIDwvZm9ybT5cclxuICAgICAgPC9EaWFsb2dDb250ZW50PlxyXG4gICAgPC9EaWFsb2c+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUZvcm0iLCJ6b2RSZXNvbHZlciIsIkJ1dHRvbiIsIkRpYWxvZyIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dEZXNjcmlwdGlvbiIsIkRpYWxvZ0Zvb3RlciIsIkRpYWxvZ0hlYWRlciIsIkRpYWxvZ1RpdGxlIiwiRGlhbG9nVHJpZ2dlciIsIklucHV0IiwiTGFiZWwiLCJTZWxlY3QiLCJTZWxlY3RDb250ZW50IiwiU2VsZWN0SXRlbSIsIlNlbGVjdFRyaWdnZXIiLCJTZWxlY3RWYWx1ZSIsIkNoZWNrYm94IiwiRmlsZVVwbG9hZCIsInRvYXN0IiwiY2xhc3Nlc0FwaSIsIkxvYWRlcjIiLCJQbHVzIiwiUmVmcmVzaEN3IiwieiIsImJhY2tlbmRTdHVkZW50U2NoZW1hIiwib2JqZWN0IiwiZmlyc3ROYW1lIiwic3RyaW5nIiwibWluIiwibWF4IiwibGFzdE5hbWUiLCJtaWRkbGVOYW1lIiwib3B0aW9uYWwiLCJlbWFpbCIsInBob25lIiwiZGF0ZU9mQmlydGgiLCJnZW5kZXIiLCJlbnVtIiwiYmxvb2RHcm91cCIsIm5hdGlvbmFsaXR5IiwicmVsaWdpb24iLCJhZGRyZXNzIiwiZW1lcmdlbmN5Q29udGFjdE5hbWUiLCJlbWVyZ2VuY3lDb250YWN0UGhvbmUiLCJlbWVyZ2VuY3lDb250YWN0UmVsYXRpb25zaGlwIiwiYWRtaXNzaW9uRGF0ZSIsImFkbWlzc2lvbk51bWJlciIsImN1cnJlbnRDbGFzc0lkIiwiYWNhZGVtaWNZZWFySWQiLCJ1dWlkIiwibWVkaWNhbENvbmRpdGlvbnMiLCJhbGxlcmdpZXMiLCJnZW5lcmF0ZVBhc3N3b3JkIiwiYm9vbGVhbiIsImRlZmF1bHQiLCJwYXNzd29yZCIsIkFkZFN0dWRlbnRNb2RhbCIsIm9uQWRkIiwib25VcGRhdGUiLCJzdHVkZW50IiwibW9kZSIsInRyaWdnZXIiLCJvcGVuIiwic2V0T3BlbiIsImlzU3VibWl0dGluZyIsInNldElzU3VibWl0dGluZyIsImNsYXNzZXMiLCJzZXRDbGFzc2VzIiwibG9hZGluZ0NsYXNzZXMiLCJzZXRMb2FkaW5nQ2xhc3NlcyIsImdlbmVyYXRlZFN0dWRlbnRJZCIsInNldEdlbmVyYXRlZFN0dWRlbnRJZCIsImlzRWRpdE1vZGUiLCJyZWdpc3RlciIsImhhbmRsZVN1Ym1pdCIsInNldFZhbHVlIiwicmVzZXQiLCJ3YXRjaCIsImZvcm1TdGF0ZSIsImVycm9ycyIsInJlc29sdmVyIiwiZGVmYXVsdFZhbHVlcyIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInNwbGl0IiwiZmV0Y2hDbGFzc2VzIiwiZ2VuZXJhdGVTdHVkZW50SWRQcmV2aWV3IiwiZm9ybURhdGEiLCJmaXJzdF9uYW1lIiwibGFzdF9uYW1lIiwibWlkZGxlX25hbWUiLCJkYXRlX29mX2JpcnRoIiwiYmxvb2RfZ3JvdXAiLCJlbWVyZ2VuY3lfY29udGFjdF9uYW1lIiwiZW1lcmdlbmN5X2NvbnRhY3RfcGhvbmUiLCJlbWVyZ2VuY3lfY29udGFjdF9yZWxhdGlvbiIsImFkbWlzc2lvbl9kYXRlIiwiYWRtaXNzaW9uX251bWJlciIsImN1cnJlbnRfY2xhc3NfaWQiLCJjbGFzc19pZCIsIm1lZGljYWxfY29uZGl0aW9ucyIsIk9iamVjdCIsImVudHJpZXMiLCJmb3JFYWNoIiwia2V5IiwidmFsdWUiLCJyZXNwb25zZSIsImdldEFsbCIsInN0YXR1cyIsImxpbWl0Iiwic29ydF9ieSIsInNvcnRfb3JkZXIiLCJzdWNjZXNzIiwiZGF0YSIsImNsYXNzZXNBcnJheSIsIkFycmF5IiwiaXNBcnJheSIsImNvbnNvbGUiLCJsb2ciLCJrZXlzIiwiaWQiLCJsZW5ndGgiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwidmFyaWFudCIsImVycm9yIiwiY3VycmVudFllYXIiLCJnZXRGdWxsWWVhciIsIm9uU3VibWl0Iiwic2VsZWN0ZWRDbGFzcyIsImZpbmQiLCJjbHMiLCJ1cGRhdGVEYXRhIiwibmFtZSIsInZhbGlkYXRpb25FcnJvcnMiLCJjbGFzc0Vycm9yIiwiZXJyIiwiZmllbGQiLCJmaXJzdEVycm9yIiwibWVzc2FnZSIsImhhbmRsZU1vZGFsQ2xvc2UiLCJpc09wZW4iLCJvbk9wZW5DaGFuZ2UiLCJhc0NoaWxkIiwiY2xhc3NOYW1lIiwiZm9ybSIsImRpdiIsInNwYW4iLCJwIiwic3R1ZGVudF9pZCIsImh0bWxGb3IiLCJwbGFjZWhvbGRlciIsInR5cGUiLCJzaXplIiwib25DbGljayIsImRpc2FibGVkIiwic2VsZWN0Iiwib3B0aW9uIiwibWFwIiwiZ3JhZGVfbGV2ZWwiLCJvblZhbHVlQ2hhbmdlIiwiaDQiLCJjaGVja2VkIiwib25DaGVja2VkQ2hhbmdlIiwiYWNjZXB0IiwibWF4RmlsZXMiLCJtYXhTaXplIiwiYXV0b1VwbG9hZCIsIm9uRmlsZVNlbGVjdCIsImZpbGVzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/modals/add-student-modal.tsx\n"));

/***/ })

});