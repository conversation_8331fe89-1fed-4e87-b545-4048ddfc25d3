{"c": ["app/layout", "app/dashboard/students/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./components/ui/progress.tsx", "(app-pages-browser)/./node_modules/@radix-ui/react-progress/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-progress/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-progress/node_modules/@radix-ui/react-context/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-progress/node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-progress/node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/attr-accept/dist/es/index.js", "(app-pages-browser)/./node_modules/file-selector/dist/es2015/file-selector.js", "(app-pages-browser)/./node_modules/file-selector/dist/es2015/file.js", "(app-pages-browser)/./node_modules/file-selector/dist/es2015/index.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js", "(app-pages-browser)/./node_modules/next/dist/build/polyfills/object-assign.js", "(app-pages-browser)/./node_modules/prop-types/checkPropTypes.js", "(app-pages-browser)/./node_modules/prop-types/factoryWithTypeCheckers.js", "(app-pages-browser)/./node_modules/prop-types/index.js", "(app-pages-browser)/./node_modules/prop-types/lib/ReactPropTypesSecret.js", "(app-pages-browser)/./node_modules/prop-types/lib/has.js", "(app-pages-browser)/./node_modules/prop-types/node_modules/react-is/cjs/react-is.development.js", "(app-pages-browser)/./node_modules/prop-types/node_modules/react-is/index.js", "(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js", "(app-pages-browser)/./node_modules/react-dropzone/dist/es/utils/index.js", "(app-pages-browser)/./src/components/ui/file-upload.tsx"]}