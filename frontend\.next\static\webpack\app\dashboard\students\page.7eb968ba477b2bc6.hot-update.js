"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./components/modals/add-student-modal.tsx":
/*!*************************************************!*\
  !*** ./components/modals/add-student-modal.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddStudentModal: () => (/* binding */ AddStudentModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/src/components/ui/file-upload */ \"(app-pages-browser)/./src/components/ui/file-upload.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _src_lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/src/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/v3/types.js\");\n/* __next_internal_client_entry_do_not_use__ AddStudentModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Backend-compatible student schema\nconst backendStudentSchema = zod__WEBPACK_IMPORTED_MODULE_12__.object({\n    firstName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'First name is required').max(100, 'First name must be less than 100 characters'),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Last name is required').max(100, 'Last name must be less than 100 characters'),\n    middleName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Middle name must be less than 100 characters').optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_12__.string().email('Please enter a valid email').optional().or(zod__WEBPACK_IMPORTED_MODULE_12__.literal('')),\n    phone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    dateOfBirth: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Date of birth is required'),\n    gender: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'male',\n        'female',\n        'other'\n    ]),\n    bloodGroup: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'A+',\n        'A-',\n        'B+',\n        'B-',\n        'AB+',\n        'AB-',\n        'O+',\n        'O-'\n    ]).optional().or(zod__WEBPACK_IMPORTED_MODULE_12__.literal('')),\n    nationality: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Nationality must be less than 100 characters').optional(),\n    religion: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Religion must be less than 100 characters').optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(500, 'Address must be less than 500 characters').optional(),\n    emergencyContactName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(200, 'Emergency contact name must be less than 200 characters').optional(),\n    emergencyContactPhone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    emergencyContactRelationship: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Emergency contact relationship must be less than 100 characters').optional(),\n    admissionDate: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Admission date is required'),\n    admissionNumber: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(50, 'Admission number must be less than 50 characters').optional(),\n    currentClassId: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Please select a class'),\n    academicYearId: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    medicalConditions: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Medical conditions must be less than 1000 characters').optional(),\n    allergies: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Allergies must be less than 1000 characters').optional(),\n    generatePassword: zod__WEBPACK_IMPORTED_MODULE_12__.boolean().default(true),\n    password: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional()\n});\nfunction AddStudentModal(param) {\n    let { onAdd, onUpdate, student, mode = 'add', trigger } = param;\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingClasses, setLoadingClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedStudentId, setGeneratedStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const isEditMode = mode === 'edit' && student;\n    const { register, handleSubmit, setValue, reset, watch, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(backendStudentSchema),\n        defaultValues: {\n            gender: 'male',\n            admissionDate: new Date().toISOString().split('T')[0],\n            generatePassword: true\n        }\n    });\n    // Fetch classes and generate student ID preview when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddStudentModal.useEffect\": ()=>{\n            if (open) {\n                fetchClasses();\n                if (!isEditMode) {\n                    generateStudentIdPreview();\n                }\n            }\n        }\n    }[\"AddStudentModal.useEffect\"], [\n        open,\n        isEditMode\n    ]);\n    // Populate form with student data when in edit mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddStudentModal.useEffect\": ()=>{\n            if (isEditMode && student && open) {\n                // Convert student data to form format\n                const formData = {\n                    firstName: student.first_name || '',\n                    lastName: student.last_name || '',\n                    middleName: student.middle_name || '',\n                    email: student.email || '',\n                    phone: student.phone || '',\n                    dateOfBirth: student.date_of_birth ? student.date_of_birth.split('T')[0] : '',\n                    gender: student.gender || 'male',\n                    bloodGroup: student.blood_group || '',\n                    nationality: student.nationality || '',\n                    religion: student.religion || '',\n                    address: student.address || '',\n                    emergencyContactName: student.emergency_contact_name || '',\n                    emergencyContactPhone: student.emergency_contact_phone || '',\n                    emergencyContactRelationship: student.emergency_contact_relation || '',\n                    admissionDate: student.admission_date ? student.admission_date.split('T')[0] : '',\n                    admissionNumber: student.admission_number || '',\n                    currentClassId: student.current_class_id || student.class_id || '',\n                    medicalConditions: student.medical_conditions || '',\n                    allergies: student.allergies || '',\n                    generatePassword: false,\n                    password: ''\n                };\n                // Set form values\n                Object.entries(formData).forEach({\n                    \"AddStudentModal.useEffect\": (param)=>{\n                        let [key, value] = param;\n                        setValue(key, value);\n                    }\n                }[\"AddStudentModal.useEffect\"]);\n            }\n        }\n    }[\"AddStudentModal.useEffect\"], [\n        isEditMode,\n        student,\n        open,\n        setValue\n    ]);\n    const fetchClasses = async ()=>{\n        try {\n            setLoadingClasses(true);\n            const response = await _src_lib_api__WEBPACK_IMPORTED_MODULE_11__.classesApi.getAll({\n                status: 'active',\n                limit: 100,\n                sort_by: 'name',\n                sort_order: 'ASC'\n            });\n            if (response.success && response.data) {\n                var _classesArray_, _classesArray_1;\n                // Handle different possible response structures\n                let classesArray = [];\n                const data = response.data;\n                if (data.classes && Array.isArray(data.classes)) {\n                    classesArray = data.classes;\n                } else if (Array.isArray(response.data)) {\n                    classesArray = response.data;\n                }\n                console.log('First class structure:', classesArray[0]);\n                console.log('Available fields:', Object.keys(classesArray[0] || {}));\n                console.log('ID field:', (_classesArray_ = classesArray[0]) === null || _classesArray_ === void 0 ? void 0 : _classesArray_.id, 'UUID field:', (_classesArray_1 = classesArray[0]) === null || _classesArray_1 === void 0 ? void 0 : _classesArray_1.uuid);\n                setClasses(classesArray);\n                if (classesArray.length === 0) {\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                        title: \"No Classes Found\",\n                        description: \"No active classes are available. Please contact an administrator.\",\n                        variant: \"destructive\"\n                    });\n                }\n            } else {\n                setClasses([]);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Warning\",\n                    description: \"Failed to load classes. Please refresh and try again.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching classes:', error);\n            setClasses([]);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Error\",\n                description: \"Failed to load classes. Please check your connection.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoadingClasses(false);\n        }\n    };\n    const generateStudentIdPreview = ()=>{\n        const currentYear = new Date().getFullYear();\n        // This is just a preview - the actual ID will be generated by the backend\n        setGeneratedStudentId(\"STU-\".concat(currentYear, \"####\"));\n    };\n    const onSubmit = async (data)=>{\n        try {\n            setIsSubmitting(true);\n            console.log('Form data being submitted:', data);\n            console.log('Selected class ID:', data.currentClassId, 'Type:', typeof data.currentClassId);\n            // Validate required fields\n            if (!data.currentClassId) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Validation Error\",\n                    description: \"Please select a class for the student.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Find selected class for better success message\n            const selectedClass = classes.find((cls)=>cls.id === data.currentClassId);\n            if (isEditMode && onUpdate) {\n                // Convert form data back to student format for update\n                const updateData = {\n                    id: student.id,\n                    first_name: data.firstName,\n                    last_name: data.lastName,\n                    middle_name: data.middleName,\n                    email: data.email,\n                    phone: data.phone,\n                    date_of_birth: data.dateOfBirth,\n                    gender: data.gender,\n                    blood_group: data.bloodGroup,\n                    nationality: data.nationality,\n                    religion: data.religion,\n                    address: data.address,\n                    emergency_contact_name: data.emergencyContactName,\n                    emergency_contact_phone: data.emergencyContactPhone,\n                    emergency_contact_relation: data.emergencyContactRelationship,\n                    admission_date: data.admissionDate,\n                    admission_number: data.admissionNumber,\n                    current_class_id: data.currentClassId,\n                    medical_conditions: data.medicalConditions,\n                    allergies: data.allergies\n                };\n                console.log('Update data being sent to backend:', updateData);\n                await onUpdate(updateData);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Student Updated Successfully! ✅\",\n                    description: \"\".concat(data.firstName, \" \").concat(data.lastName, \"'s information has been updated.\")\n                });\n            } else if (onAdd) {\n                console.log('Form data being sent to backend:', data);\n                await onAdd(data);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Student Added Successfully! 🎉\",\n                    description: \"\".concat(data.firstName, \" \").concat(data.lastName, \" has been enrolled in \").concat((selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.name) || 'the selected class', \".\")\n                });\n            }\n            // Reset form and close modal\n            reset();\n            setOpen(false);\n        } catch (error) {\n            var _error_response_data, _error_response, _error_response_data1, _error_response1;\n            console.error('Error adding student:', error);\n            // Handle validation errors from backend\n            if ((error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.errors) && Array.isArray(error.response.data.errors)) {\n                const validationErrors = error.response.data.errors;\n                const classError = validationErrors.find((err)=>err.field === 'currentClassId');\n                if (classError) {\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                        title: \"Class Selection Error\",\n                        description: \"The selected class is invalid. Please select a different class and try again.\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    // Show first validation error\n                    const firstError = validationErrors[0];\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                        title: \"Validation Error\",\n                        description: \"\".concat(firstError.message || 'Please check your input and try again.'),\n                        variant: \"destructive\"\n                    });\n                }\n            } else if (error === null || error === void 0 ? void 0 : (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) {\n                // Handle general error messages\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Error\",\n                    description: error.response.data.message,\n                    variant: \"destructive\"\n                });\n            } else {\n                // Handle unknown errors\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Error\",\n                    description: \"Failed to \".concat(isEditMode ? 'update' : 'add', \" student. Please try again.\"),\n                    variant: \"destructive\"\n                });\n            }\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Reset form when modal is closed\n    const handleModalClose = (isOpen)=>{\n        setOpen(isOpen);\n        if (!isOpen) {\n            reset();\n            setGeneratedStudentId('');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n        open: open,\n        onOpenChange: handleModalClose,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTrigger, {\n                asChild: true,\n                children: trigger || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, this),\n                        \"Add Student\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                className: \"max-h-[90vh] max-w-[800px] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                children: isEditMode ? 'Edit Student' : 'Add New Student'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                children: isEditMode ? 'Update the student information below. Click save when you\\'re done.' : 'Enter the student information below. Click save when you\\'re done.'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit(onSubmit),\n                        className: \"space-y-4 mt-4\",\n                        children: [\n                            !isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-blue-800\",\n                                                children: \"Student ID Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-600 mt-1\",\n                                        children: [\n                                            \"Student ID will be automatically generated: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono font-semibold\",\n                                                children: generatedStudentId\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 61\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-500 mt-1\",\n                                        children: \"The actual ID will be assigned when the student is created\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this),\n                            isEditMode && student && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-green-800\",\n                                                children: \"Current Student ID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-600 mt-1\",\n                                        children: [\n                                            \"Student ID: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono font-semibold\",\n                                                children: student.student_id\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this),\n                                    student.admission_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-green-500 mt-1\",\n                                        children: [\n                                            \"Admission Number: \",\n                                            student.admission_number\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionNumber\",\n                                                children: \"Admission Number (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionNumber\",\n                                                ...register('admissionNumber'),\n                                                className: errors.admissionNumber ? 'border-red-500' : '',\n                                                placeholder: \"Leave empty to auto-generate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionNumber.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"currentClassId\",\n                                                        children: \"Class *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: fetchClasses,\n                                                        disabled: loadingClasses,\n                                                        className: \"h-6 px-2 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1 \".concat(loadingClasses ? 'animate-spin' : '')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Refresh\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                ...register('currentClassId'),\n                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 \".concat(errors.currentClassId ? 'border-red-500' : ''),\n                                                disabled: loadingClasses,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: loadingClasses ? \"Loading classes...\" : \"Select class\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    classes.map((cls)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: cls.id,\n                                                            children: cls.grade_level ? \"\".concat(cls.name, \" (\").concat(cls.grade_level, \")\") : cls.name\n                                                        }, cls.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.currentClassId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.currentClassId.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this),\n                                            classes.length === 0 && !loadingClasses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-amber-600\",\n                                                children: \"No classes found. Please contact an administrator to create classes first.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"firstName\",\n                                                children: \"First Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"firstName\",\n                                                ...register('firstName'),\n                                                className: errors.firstName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.firstName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"middleName\",\n                                                children: \"Middle Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"middleName\",\n                                                ...register('middleName'),\n                                                className: errors.middleName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.middleName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.middleName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"lastName\",\n                                                children: \"Last Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"lastName\",\n                                                ...register('lastName'),\n                                                className: errors.lastName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.lastName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"email\",\n                                                children: \"Email (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                ...register('email'),\n                                                className: errors.email ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.email.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"phone\",\n                                                children: \"Phone (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"phone\",\n                                                ...register('phone'),\n                                                className: errors.phone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.phone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"dateOfBirth\",\n                                                children: \"Date of Birth *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"dateOfBirth\",\n                                                type: \"date\",\n                                                ...register('dateOfBirth'),\n                                                className: errors.dateOfBirth ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.dateOfBirth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.dateOfBirth.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"gender\",\n                                                children: \"Gender *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('gender') || 'male',\n                                                onValueChange: (value)=>setValue('gender', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.gender ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select gender\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"male\",\n                                                                children: \"Male\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"female\",\n                                                                children: \"Female\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"other\",\n                                                                children: \"Other\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.gender && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.gender.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionDate\",\n                                                children: \"Admission Date *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionDate\",\n                                                type: \"date\",\n                                                ...register('admissionDate'),\n                                                className: errors.admissionDate ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionDate.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"bloodGroup\",\n                                                children: \"Blood Group\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('bloodGroup') || '',\n                                                onValueChange: (value)=>setValue('bloodGroup', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.bloodGroup ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select blood group\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A+\",\n                                                                children: \"A+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A-\",\n                                                                children: \"A-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B+\",\n                                                                children: \"B+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B-\",\n                                                                children: \"B-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB+\",\n                                                                children: \"AB+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB-\",\n                                                                children: \"AB-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O+\",\n                                                                children: \"O+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O-\",\n                                                                children: \"O-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.bloodGroup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.bloodGroup.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"nationality\",\n                                                children: \"Nationality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"nationality\",\n                                                ...register('nationality'),\n                                                className: errors.nationality ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Indian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.nationality && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.nationality.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"religion\",\n                                                children: \"Religion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"religion\",\n                                                ...register('religion'),\n                                                className: errors.religion ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.religion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.religion.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"address\",\n                                        children: \"Address (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        id: \"address\",\n                                        ...register('address'),\n                                        className: errors.address ? 'border-red-500' : ''\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600\",\n                                        children: errors.address.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactName\",\n                                                children: \"Emergency Contact Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactName\",\n                                                ...register('emergencyContactName'),\n                                                className: errors.emergencyContactName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactPhone\",\n                                                children: \"Emergency Contact Phone\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactPhone\",\n                                                ...register('emergencyContactPhone'),\n                                                className: errors.emergencyContactPhone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactPhone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactPhone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactRelationship\",\n                                                children: \"Relationship\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactRelationship\",\n                                                ...register('emergencyContactRelationship'),\n                                                className: errors.emergencyContactRelationship ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Father, Mother, Guardian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactRelationship && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactRelationship.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"medicalConditions\",\n                                                children: \"Medical Conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"medicalConditions\",\n                                                ...register('medicalConditions'),\n                                                className: errors.medicalConditions ? 'border-red-500' : '',\n                                                placeholder: \"Any medical conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.medicalConditions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.medicalConditions.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"allergies\",\n                                                children: \"Allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"allergies\",\n                                                ...register('allergies'),\n                                                className: errors.allergies ? 'border-red-500' : '',\n                                                placeholder: \"Any known allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.allergies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.allergies.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 11\n                            }, this),\n                            !isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 border-t pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Password Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                id: \"generatePassword\",\n                                                checked: watch('generatePassword'),\n                                                onCheckedChange: (checked)=>setValue('generatePassword', !!checked)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"generatePassword\",\n                                                className: \"text-sm\",\n                                                children: \"Generate password automatically (recommended)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 15\n                                    }, this),\n                                    !watch('generatePassword') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"password\",\n                                                children: \"Custom Password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"password\",\n                                                type: \"password\",\n                                                ...register('password'),\n                                                className: errors.password ? 'border-red-500' : '',\n                                                placeholder: \"Enter custom password (min 8 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 19\n                                            }, this),\n                                            errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.password.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"profile_picture\",\n                                        children: \"Profile Picture (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__.FileUpload, {\n                                        accept: \"image/*\",\n                                        maxFiles: 1,\n                                        maxSize: 5 * 1024 * 1024,\n                                        autoUpload: false,\n                                        onFileSelect: (files)=>{\n                                            // Handle file selection for future implementation\n                                            console.log('Selected files:', files);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Upload a profile picture for the student (max 5MB)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: ()=>setOpen(false),\n                                        disabled: isSubmitting,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isEditMode ? 'Updating...' : 'Adding...'\n                                            ]\n                                        }, void 0, true) : isEditMode ? 'Update Student' : 'Add Student'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 742,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n        lineNumber: 333,\n        columnNumber: 5\n    }, this);\n}\n_s(AddStudentModal, \"/v7JLUIS8G/it21M1VUDKZENywk=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm\n    ];\n});\n_c = AddStudentModal;\nvar _c;\n$RefreshReg$(_c, \"AddStudentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/modals/add-student-modal.tsx\n"));

/***/ })

});