"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./app/dashboard/students/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/students/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/bulk-delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/bulk-delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/add-student-modal */ \"(app-pages-browser)/./components/modals/add-student-modal.tsx\");\n/* harmony import */ var _components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/student-document-modal */ \"(app-pages-browser)/./components/modals/student-document-modal.tsx\");\n/* harmony import */ var _components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/print-student-modal */ \"(app-pages-browser)/./components/modals/print-student-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// import { EditStudentModal } from \"@/components/modals/edit-student-modal\" // Using unified AddStudentModal instead\n\n\n\n\n\n\n\n\nfunction StudentsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '';\n        try {\n            var _response_data, _response_data1;\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.getAll({\n                page,\n                limit,\n                search,\n                sort_by: 'first_name',\n                sort_order: 'ASC'\n            });\n            setStudents(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.students) || []);\n            if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.pagination) {\n                setPagination(response.data.pagination);\n            }\n        } catch (error) {\n            console.error('Error fetching students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch students. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentsPage.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentsPage.useEffect\"], []);\n    const handleSaveStudent = async (updatedStudent)=>{\n        try {\n            console.log('handleSaveStudent called with:', updatedStudent);\n            // Check if data is already in camelCase (from modal) or snake_case (from other sources)\n            const isFromModal = updatedStudent.firstName !== undefined;\n            let updateData;\n            let studentId;\n            if (isFromModal) {\n                // Data from modal is already in camelCase format\n                updateData = {\n                    firstName: updatedStudent.firstName,\n                    lastName: updatedStudent.lastName,\n                    middleName: updatedStudent.middleName,\n                    email: updatedStudent.email,\n                    phone: updatedStudent.phone,\n                    dateOfBirth: updatedStudent.dateOfBirth,\n                    gender: updatedStudent.gender,\n                    address: updatedStudent.address,\n                    bloodGroup: updatedStudent.bloodGroup,\n                    nationality: updatedStudent.nationality,\n                    religion: updatedStudent.religion,\n                    medicalConditions: updatedStudent.medicalConditions,\n                    emergencyContactName: updatedStudent.emergencyContactName,\n                    emergencyContactPhone: updatedStudent.emergencyContactPhone,\n                    emergencyContactRelationship: updatedStudent.emergencyContactRelationship,\n                    currentClassId: updatedStudent.currentClassId,\n                    status: updatedStudent.status\n                };\n                studentId = updatedStudent.id;\n            } else {\n                // Data from other sources - convert snake_case to camelCase\n                updateData = {\n                    firstName: updatedStudent.first_name,\n                    lastName: updatedStudent.last_name,\n                    middleName: updatedStudent.middle_name,\n                    email: updatedStudent.email,\n                    phone: updatedStudent.phone,\n                    dateOfBirth: updatedStudent.date_of_birth,\n                    gender: updatedStudent.gender,\n                    address: updatedStudent.address,\n                    bloodGroup: updatedStudent.blood_group,\n                    nationality: updatedStudent.nationality,\n                    religion: updatedStudent.religion,\n                    medicalConditions: updatedStudent.medical_conditions,\n                    emergencyContactName: updatedStudent.emergency_contact_name,\n                    emergencyContactPhone: updatedStudent.emergency_contact_phone,\n                    emergencyContactRelationship: updatedStudent.emergency_contact_relation,\n                    currentClassId: updatedStudent.current_class_id || updatedStudent.class_id,\n                    status: updatedStudent.status\n                };\n                studentId = updatedStudent.id;\n            }\n            console.log('Calling API with studentId:', studentId, 'updateData:', updateData);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.update(String(studentId), updateData);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            const firstName = isFromModal ? updatedStudent.firstName : updatedStudent.first_name;\n            const lastName = isFromModal ? updatedStudent.lastName : updatedStudent.last_name;\n            toast({\n                title: \"Student Updated\",\n                description: \"\".concat(firstName, \" \").concat(lastName, \"'s information has been updated successfully.\")\n            });\n        } catch (error) {\n            console.error('Error updating student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteStudent = async (id)=>{\n        try {\n            const stringId = String(id);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.delete(stringId);\n            setStudents((prev)=>prev.filter((student)=>String(student.id) !== stringId));\n            toast({\n                title: \"Student Deleted\",\n                description: \"Student has been deleted successfully.\"\n            });\n        } catch (error) {\n            console.error('Error deleting student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddStudent = async (newStudent)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.create(newStudent);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Added\",\n                description: \"\".concat(newStudent.firstName, \" \").concat(newStudent.lastName, \" has been added successfully.\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        try {\n            // Delete students one by one (could be optimized with bulk delete API)\n            await Promise.all(selectedRows.map((id)=>_lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.delete(String(id))));\n            setStudents((prev)=>prev.filter((student)=>!selectedRows.includes(String(student.id))));\n            toast({\n                title: \"Students Deleted\",\n                description: \"\".concat(selectedRows.length, \" students have been deleted successfully.\")\n            });\n            setSelectedRows([]);\n        } catch (error) {\n            console.error('Error deleting students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete students. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportStudents = async ()=>{\n        try {\n            if (students.length === 0) {\n                toast({\n                    title: \"No Data\",\n                    description: \"No students to export.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Generate CSV export\n            const csvData = students.map((student)=>({\n                    'Student ID': student.student_id || '',\n                    'Name': \"\".concat(student.first_name, \" \").concat(student.last_name),\n                    'Email': student.email || '',\n                    'Phone': student.phone || '',\n                    'Gender': student.gender || '',\n                    'Date of Birth': student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : '',\n                    'Blood Group': student.blood_group || '',\n                    'Nationality': student.nationality || '',\n                    'Religion': student.religion || '',\n                    'Address': student.address || '',\n                    'Emergency Contact': student.emergency_contact_name || '',\n                    'Emergency Phone': student.emergency_contact_phone || '',\n                    'Class': student.class_name || '',\n                    'Grade Level': student.grade_level || '',\n                    'Academic Year': student.academic_year || '',\n                    'Status': student.user_status || '',\n                    'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '',\n                    'Admission Number': student.admission_number || '',\n                    'Roll Number': student.roll_number || '',\n                    'Medical Conditions': student.medical_conditions || '',\n                    'Transport Required': student.transport_required ? 'Yes' : 'No',\n                    'Hostel Required': student.hostel_required ? 'Yes' : 'No'\n                }));\n            // Helper function to escape CSV values\n            const escapeCSV = (value)=>{\n                if (value === null || value === undefined) return '';\n                const str = String(value);\n                if (str.includes(',') || str.includes('\"') || str.includes('\\n')) {\n                    return '\"'.concat(str.replace(/\"/g, '\"\"'), '\"');\n                }\n                return str;\n            };\n            const csvContent = [\n                Object.keys(csvData[0]).join(','),\n                ...csvData.map((row)=>Object.values(row).map(escapeCSV).join(','))\n            ].join('\\n');\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"students_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            a.click();\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Export Successful\",\n                description: \"Student data has been exported successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to export student data.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImportStudents = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = '.csv';\n        input.onchange = async (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (!file) return;\n            try {\n                const text = await file.text();\n                const lines = text.split('\\n').filter((line)=>line.trim());\n                if (lines.length < 2) {\n                    toast({\n                        title: \"Invalid File\",\n                        description: \"CSV file must have at least a header and one data row.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Parse CSV (basic implementation)\n                const headers = lines[0].split(',').map((h)=>h.trim());\n                const requiredHeaders = [\n                    'firstName',\n                    'lastName',\n                    'email',\n                    'dateOfBirth',\n                    'gender'\n                ];\n                const missingHeaders = requiredHeaders.filter((h)=>!headers.includes(h));\n                if (missingHeaders.length > 0) {\n                    toast({\n                        title: \"Invalid CSV Format\",\n                        description: \"Missing required columns: \".concat(missingHeaders.join(', ')),\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                const students = lines.slice(1).map((line)=>{\n                    const values = line.split(',').map((v)=>v.trim().replace(/^\"|\"$/g, ''));\n                    const student = {};\n                    headers.forEach((header, index)=>{\n                        student[header] = values[index] || '';\n                    });\n                    return student;\n                });\n                // Validate and import students\n                const validStudents = students.filter((student)=>student.firstName && student.lastName && student.email && student.dateOfBirth && student.gender);\n                if (validStudents.length === 0) {\n                    toast({\n                        title: \"No Valid Students\",\n                        description: \"No valid student records found in the CSV file.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Use bulk create API\n                await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.bulkCreate(validStudents);\n                toast({\n                    title: \"Import Successful\",\n                    description: \"Successfully imported \".concat(validStudents.length, \" students.\")\n                });\n                // Refresh the students list\n                await fetchStudents();\n            } catch (error) {\n                console.error('Import error:', error);\n                toast({\n                    title: \"Import Failed\",\n                    description: \"Failed to import students. Please check the file format.\",\n                    variant: \"destructive\"\n                });\n            }\n        };\n        input.click();\n    };\n    const handlePrintAll = ()=>{\n        if (students.length === 0) {\n            toast({\n                title: \"No Data\",\n                description: \"No students to print.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create a new window for printing\n        const printWindow = window.open('', '_blank');\n        if (!printWindow) return;\n        const printContent = '\\n      <!DOCTYPE html>\\n      <html>\\n        <head>\\n          <title>Students List</title>\\n          <style>\\n            body {\\n              font-family: Arial, sans-serif;\\n              margin: 20px;\\n              line-height: 1.4;\\n            }\\n            .header {\\n              text-align: center;\\n              border-bottom: 2px solid #333;\\n              padding-bottom: 20px;\\n              margin-bottom: 30px;\\n            }\\n            table {\\n              width: 100%;\\n              border-collapse: collapse;\\n              margin-top: 20px;\\n            }\\n            th, td {\\n              border: 1px solid #ddd;\\n              padding: 8px;\\n              text-align: left;\\n              font-size: 12px;\\n            }\\n            th {\\n              background-color: #f5f5f5;\\n              font-weight: bold;\\n            }\\n            .footer {\\n              margin-top: 30px;\\n              text-align: center;\\n              font-size: 10px;\\n              color: #666;\\n            }\\n            @media print {\\n              body { margin: 0; }\\n              .no-print { display: none; }\\n            }\\n          </style>\\n        </head>\\n        <body>\\n          <div class=\"header\">\\n            <h1>Students List</h1>\\n            <p>Total Students: '.concat(students.length, \"</p>\\n            <p>Generated on: \").concat(new Date().toLocaleDateString(), \"</p>\\n          </div>\\n\\n          <table>\\n            <thead>\\n              <tr>\\n                <th>Student ID</th>\\n                <th>Name</th>\\n                <th>Email</th>\\n                <th>Phone</th>\\n                <th>Class</th>\\n                <th>Status</th>\\n                <th>Admission Date</th>\\n              </tr>\\n            </thead>\\n            <tbody>\\n              \").concat(students.map((student)=>\"\\n                <tr>\\n                  <td>\".concat(student.student_id || '', \"</td>\\n                  <td>\").concat(student.first_name, \" \").concat(student.last_name, \"</td>\\n                  <td>\").concat(student.email || '', \"</td>\\n                  <td>\").concat(student.phone || '', \"</td>\\n                  <td>\").concat(student.class_name || '', \"</td>\\n                  <td>\").concat(student.user_status || '', \"</td>\\n                  <td>\").concat(student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '', \"</td>\\n                </tr>\\n              \")).join(''), '\\n            </tbody>\\n          </table>\\n\\n          <div class=\"footer\">\\n            <p>School Management System - Students Report</p>\\n          </div>\\n        </body>\\n      </html>\\n    ');\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        printWindow.focus();\n        // Wait for content to load then print\n        setTimeout(()=>{\n            printWindow.print();\n            printWindow.close();\n        }, 250);\n        toast({\n            title: \"Print Started\",\n            description: \"Preparing student list for printing.\"\n        });\n    };\n    const columns = [\n        {\n            id: \"select\",\n            header: (param)=>{\n                let { table } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected() && \"indeterminate\",\n                    onCheckedChange: (value)=>table.toggleAllPageRowsSelected(!!value),\n                    \"aria-label\": \"Select all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 536,\n                    columnNumber: 9\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: row.getIsSelected(),\n                    onCheckedChange: (value)=>row.toggleSelected(!!value),\n                    \"aria-label\": \"Select row\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 543,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false,\n            enableHiding: false\n        },\n        {\n            accessorKey: \"student_id\",\n            header: \"Student ID\"\n        },\n        {\n            accessorKey: \"photo\",\n            header: \"Photo\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                    className: \"h-10 w-10\",\n                    children: student.profile_picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: student.profile_picture || \"/placeholder.svg\",\n                        alt: \"\".concat(student.first_name, \" \").concat(student.last_name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        children: [\n                            student.first_name.charAt(0),\n                            student.last_name.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 562,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"first_name\",\n            header: \"Name\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return \"\".concat(student.first_name, \" \").concat(student.last_name);\n            }\n        },\n        {\n            accessorKey: \"class_name\",\n            header: \"Class\"\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Email\",\n            cell: (param)=>{\n                let { row } = param;\n                const email = row.getValue(\"email\");\n                return email || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: \"Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const phone = row.getValue(\"phone\");\n                return phone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"gender\",\n            header: \"Gender\",\n            cell: (param)=>{\n                let { row } = param;\n                const gender = row.getValue(\"gender\");\n                return gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"date_of_birth\",\n            header: \"Date of Birth\",\n            cell: (param)=>{\n                let { row } = param;\n                const dob = row.getValue(\"date_of_birth\");\n                return dob ? new Date(dob).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"admission_date\",\n            header: \"Admission Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const admissionDate = row.getValue(\"admission_date\");\n                return admissionDate ? new Date(admissionDate).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_name\",\n            header: \"Emergency Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const contact = row.getValue(\"emergency_contact_name\");\n                return contact || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_phone\",\n            header: \"Emergency Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const contactPhone = row.getValue(\"emergency_contact_phone\");\n                return contactPhone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"user_status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const status = row.getValue(\"user_status\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: status === \"active\" ? \"default\" : \"outline\",\n                    children: status || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 645,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/students/\".concat(student.id)),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 15\n                                }, this),\n                                \"Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 654,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_12__.StudentDocumentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Docs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_13__.PrintStudentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 672,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__.AddStudentModal, {\n                            student: student,\n                            mode: \"edit\",\n                            onUpdate: handleSaveStudent,\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 683,\n                                columnNumber: 17\n                            }, void 0)\n                        }, \"edit-\".concat(student.id), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 677,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_9__.DeleteConfirmationModal, {\n                            title: \"Delete Student\",\n                            description: \"Are you sure you want to delete \".concat(student.first_name, \" \").concat(student.last_name, \"? This action cannot be undone.\"),\n                            onConfirm: ()=>handleDeleteStudent(student.id),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 694,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 693,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 688,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 653,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 708,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Loading students...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 709,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm text-muted-foreground\",\n                        children: \"Please wait while we fetch the student data.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 710,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 707,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n            lineNumber: 706,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleImportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 721,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleExportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrintAll,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Print All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 729,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__.AddStudentModal, {\n                                onAdd: handleAddStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Add Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 733,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 718,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"View and manage all students in the system\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 747,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__.BulkDeleteConfirmationModal, {\n                                    title: \"Delete Selected Students\",\n                                    description: \"Are you sure you want to delete \".concat(selectedRows.length, \" selected students? This action cannot be undone.\"),\n                                    count: selectedRows.length,\n                                    onConfirm: handleBulkDelete\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 746,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 745,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-gray-600\",\n                                        children: \"Loading students...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 762,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 761,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                            columns: columns,\n                            data: students,\n                            searchKey: \"first_name\",\n                            searchPlaceholder: \"Search students...\",\n                            onPrint: handlePrintAll,\n                            onExport: handleExportStudents\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 768,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 759,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 744,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n        lineNumber: 717,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentsPage, \"2mPRm9Jx2cynZ5f7wAONxa8oUKE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = StudentsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/students/page.tsx\n"));

/***/ })

});