"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./components/modals/add-student-modal.tsx":
/*!*************************************************!*\
  !*** ./components/modals/add-student-modal.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddStudentModal: () => (/* binding */ AddStudentModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/src/components/ui/file-upload */ \"(app-pages-browser)/./src/components/ui/file-upload.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/v3/types.js\");\n/* __next_internal_client_entry_do_not_use__ AddStudentModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Backend-compatible student schema\nconst backendStudentSchema = zod__WEBPACK_IMPORTED_MODULE_12__.object({\n    firstName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'First name is required').max(100, 'First name must be less than 100 characters'),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Last name is required').max(100, 'Last name must be less than 100 characters'),\n    middleName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Middle name must be less than 100 characters').optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_12__.string().email('Please enter a valid email').optional().or(zod__WEBPACK_IMPORTED_MODULE_12__.literal('')),\n    phone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    dateOfBirth: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Date of birth is required'),\n    gender: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'male',\n        'female',\n        'other'\n    ]),\n    bloodGroup: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'A+',\n        'A-',\n        'B+',\n        'B-',\n        'AB+',\n        'AB-',\n        'O+',\n        'O-'\n    ]).optional().or(zod__WEBPACK_IMPORTED_MODULE_12__.literal('')),\n    nationality: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Nationality must be less than 100 characters').optional(),\n    religion: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Religion must be less than 100 characters').optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(500, 'Address must be less than 500 characters').optional(),\n    emergencyContactName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(200, 'Emergency contact name must be less than 200 characters').optional(),\n    emergencyContactPhone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    emergencyContactRelationship: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Emergency contact relationship must be less than 100 characters').optional(),\n    admissionDate: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Admission date is required'),\n    admissionNumber: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(50, 'Admission number must be less than 50 characters').optional(),\n    currentClassId: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Please select a class'),\n    academicYearId: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    medicalConditions: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Medical conditions must be less than 1000 characters').optional(),\n    allergies: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Allergies must be less than 1000 characters').optional(),\n    generatePassword: zod__WEBPACK_IMPORTED_MODULE_12__.boolean().default(true),\n    password: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional()\n});\nfunction AddStudentModal(param) {\n    let { onAdd, onUpdate, student, mode = 'add', trigger } = param;\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingClasses, setLoadingClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedStudentId, setGeneratedStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const isEditMode = mode === 'edit' && student;\n    const { register, handleSubmit, setValue, reset, watch, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(backendStudentSchema),\n        defaultValues: {\n            gender: 'male',\n            admissionDate: new Date().toISOString().split('T')[0],\n            generatePassword: true\n        }\n    });\n    // Fetch classes and generate student ID preview when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddStudentModal.useEffect\": ()=>{\n            if (open) {\n                fetchClasses();\n                if (!isEditMode) {\n                    generateStudentIdPreview();\n                }\n            }\n        }\n    }[\"AddStudentModal.useEffect\"], [\n        open,\n        isEditMode\n    ]);\n    // Populate form with student data when in edit mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddStudentModal.useEffect\": ()=>{\n            if (isEditMode && student && open) {\n                // Convert student data to form format\n                const formData = {\n                    firstName: student.first_name || '',\n                    lastName: student.last_name || '',\n                    middleName: student.middle_name || '',\n                    email: student.email || '',\n                    phone: student.phone || '',\n                    dateOfBirth: student.date_of_birth ? student.date_of_birth.split('T')[0] : '',\n                    gender: student.gender || 'male',\n                    bloodGroup: student.blood_group || '',\n                    nationality: student.nationality || '',\n                    religion: student.religion || '',\n                    address: student.address || '',\n                    emergencyContactName: student.emergency_contact_name || '',\n                    emergencyContactPhone: student.emergency_contact_phone || '',\n                    emergencyContactRelationship: student.emergency_contact_relation || '',\n                    admissionDate: student.admission_date ? student.admission_date.split('T')[0] : '',\n                    admissionNumber: student.admission_number || '',\n                    currentClassId: student.current_class_id || student.class_id || '',\n                    medicalConditions: student.medical_conditions || '',\n                    allergies: student.allergies || '',\n                    generatePassword: false,\n                    password: ''\n                };\n                // Set form values\n                Object.entries(formData).forEach({\n                    \"AddStudentModal.useEffect\": (param)=>{\n                        let [key, value] = param;\n                        setValue(key, value);\n                    }\n                }[\"AddStudentModal.useEffect\"]);\n            }\n        }\n    }[\"AddStudentModal.useEffect\"], [\n        isEditMode,\n        student,\n        open,\n        setValue\n    ]);\n    const fetchClasses = async ()=>{\n        try {\n            setLoadingClasses(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_11__.classesApi.getAll({\n                status: 'active',\n                limit: 100,\n                sort_by: 'name',\n                sort_order: 'ASC'\n            });\n            if (response.success && response.data) {\n                var _classesArray_, _classesArray_1;\n                // Handle different possible response structures\n                let classesArray = [];\n                const data = response.data;\n                if (data.classes && Array.isArray(data.classes)) {\n                    classesArray = data.classes;\n                } else if (Array.isArray(response.data)) {\n                    classesArray = response.data;\n                }\n                console.log('First class structure:', classesArray[0]);\n                console.log('Available fields:', Object.keys(classesArray[0] || {}));\n                console.log('ID field:', (_classesArray_ = classesArray[0]) === null || _classesArray_ === void 0 ? void 0 : _classesArray_.id, 'UUID field:', (_classesArray_1 = classesArray[0]) === null || _classesArray_1 === void 0 ? void 0 : _classesArray_1.uuid);\n                setClasses(classesArray);\n                if (classesArray.length === 0) {\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                        title: \"No Classes Found\",\n                        description: \"No active classes are available. Please contact an administrator.\",\n                        variant: \"destructive\"\n                    });\n                }\n            } else {\n                setClasses([]);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Warning\",\n                    description: \"Failed to load classes. Please refresh and try again.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching classes:', error);\n            setClasses([]);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Error\",\n                description: \"Failed to load classes. Please check your connection.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoadingClasses(false);\n        }\n    };\n    const generateStudentIdPreview = ()=>{\n        const currentYear = new Date().getFullYear();\n        // This is just a preview - the actual ID will be generated by the backend\n        setGeneratedStudentId(\"STU-\".concat(currentYear, \"####\"));\n    };\n    const onSubmit = async (data)=>{\n        try {\n            setIsSubmitting(true);\n            console.log('Form data being submitted:', data);\n            console.log('Selected class ID:', data.currentClassId, 'Type:', typeof data.currentClassId);\n            // Validate required fields\n            if (!data.currentClassId) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Validation Error\",\n                    description: \"Please select a class for the student.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Find selected class for better success message\n            const selectedClass = classes.find((cls)=>cls.id === data.currentClassId);\n            if (isEditMode && onUpdate) {\n                // Convert form data back to student format for update\n                const updateData = {\n                    id: student.id,\n                    first_name: data.firstName,\n                    last_name: data.lastName,\n                    middle_name: data.middleName,\n                    email: data.email,\n                    phone: data.phone,\n                    date_of_birth: data.dateOfBirth,\n                    gender: data.gender,\n                    blood_group: data.bloodGroup,\n                    nationality: data.nationality,\n                    religion: data.religion,\n                    address: data.address,\n                    emergency_contact_name: data.emergencyContactName,\n                    emergency_contact_phone: data.emergencyContactPhone,\n                    emergency_contact_relation: data.emergencyContactRelationship,\n                    admission_date: data.admissionDate,\n                    admission_number: data.admissionNumber,\n                    current_class_id: data.currentClassId,\n                    medical_conditions: data.medicalConditions,\n                    allergies: data.allergies\n                };\n                console.log('Update data being sent to backend:', updateData);\n                await onUpdate(updateData);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Student Updated Successfully! ✅\",\n                    description: \"\".concat(data.firstName, \" \").concat(data.lastName, \"'s information has been updated.\")\n                });\n            } else if (onAdd) {\n                console.log('Form data being sent to backend:', data);\n                await onAdd(data);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Student Added Successfully! 🎉\",\n                    description: \"\".concat(data.firstName, \" \").concat(data.lastName, \" has been enrolled in \").concat((selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.name) || 'the selected class', \".\")\n                });\n            }\n            // Reset form and close modal\n            reset();\n            setOpen(false);\n        } catch (error) {\n            var _error_response_data, _error_response, _error_response_data1, _error_response1;\n            console.error('Error adding student:', error);\n            // Handle validation errors from backend\n            if ((error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.errors) && Array.isArray(error.response.data.errors)) {\n                const validationErrors = error.response.data.errors;\n                const classError = validationErrors.find((err)=>err.field === 'currentClassId');\n                if (classError) {\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                        title: \"Class Selection Error\",\n                        description: \"The selected class is invalid. Please select a different class and try again.\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    // Show first validation error\n                    const firstError = validationErrors[0];\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                        title: \"Validation Error\",\n                        description: \"\".concat(firstError.message || 'Please check your input and try again.'),\n                        variant: \"destructive\"\n                    });\n                }\n            } else if (error === null || error === void 0 ? void 0 : (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) {\n                // Handle general error messages\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Error\",\n                    description: error.response.data.message,\n                    variant: \"destructive\"\n                });\n            } else {\n                // Handle unknown errors\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Error\",\n                    description: \"Failed to \".concat(isEditMode ? 'update' : 'add', \" student. Please try again.\"),\n                    variant: \"destructive\"\n                });\n            }\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Reset form when modal is closed\n    const handleModalClose = (isOpen)=>{\n        setOpen(isOpen);\n        if (!isOpen) {\n            reset();\n            setGeneratedStudentId('');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n        open: open,\n        onOpenChange: handleModalClose,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTrigger, {\n                asChild: true,\n                children: trigger || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, this),\n                        \"Add Student\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                className: \"max-h-[90vh] max-w-[800px] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                children: isEditMode ? 'Edit Student' : 'Add New Student'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                children: isEditMode ? 'Update the student information below. Click save when you\\'re done.' : 'Enter the student information below. Click save when you\\'re done.'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit(onSubmit),\n                        className: \"space-y-4 mt-4\",\n                        children: [\n                            !isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-blue-800\",\n                                                children: \"Student ID Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-600 mt-1\",\n                                        children: [\n                                            \"Student ID will be automatically generated: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono font-semibold\",\n                                                children: generatedStudentId\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 61\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-500 mt-1\",\n                                        children: \"The actual ID will be assigned when the student is created\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this),\n                            isEditMode && student && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-green-800\",\n                                                children: \"Current Student ID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-600 mt-1\",\n                                        children: [\n                                            \"Student ID: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono font-semibold\",\n                                                children: student.student_id\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this),\n                                    student.admission_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-green-500 mt-1\",\n                                        children: [\n                                            \"Admission Number: \",\n                                            student.admission_number\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionNumber\",\n                                                children: \"Admission Number (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionNumber\",\n                                                ...register('admissionNumber'),\n                                                className: errors.admissionNumber ? 'border-red-500' : '',\n                                                placeholder: \"Leave empty to auto-generate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionNumber.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"currentClassId\",\n                                                        children: \"Class *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: fetchClasses,\n                                                        disabled: loadingClasses,\n                                                        className: \"h-6 px-2 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1 \".concat(loadingClasses ? 'animate-spin' : '')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Refresh\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                ...register('currentClassId'),\n                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 \".concat(errors.currentClassId ? 'border-red-500' : ''),\n                                                disabled: loadingClasses,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: loadingClasses ? \"Loading classes...\" : \"Select class\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    classes.map((cls)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: cls.id,\n                                                            children: cls.grade_level ? \"\".concat(cls.name, \" (\").concat(cls.grade_level, \")\") : cls.name\n                                                        }, cls.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.currentClassId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.currentClassId.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this),\n                                            classes.length === 0 && !loadingClasses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-amber-600\",\n                                                children: \"No classes found. Please contact an administrator to create classes first.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"firstName\",\n                                                children: \"First Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"firstName\",\n                                                ...register('firstName'),\n                                                className: errors.firstName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.firstName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"middleName\",\n                                                children: \"Middle Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"middleName\",\n                                                ...register('middleName'),\n                                                className: errors.middleName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.middleName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.middleName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"lastName\",\n                                                children: \"Last Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"lastName\",\n                                                ...register('lastName'),\n                                                className: errors.lastName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.lastName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"email\",\n                                                children: \"Email (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                ...register('email'),\n                                                className: errors.email ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.email.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"phone\",\n                                                children: \"Phone (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"phone\",\n                                                ...register('phone'),\n                                                className: errors.phone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.phone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"dateOfBirth\",\n                                                children: \"Date of Birth *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"dateOfBirth\",\n                                                type: \"date\",\n                                                ...register('dateOfBirth'),\n                                                className: errors.dateOfBirth ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.dateOfBirth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.dateOfBirth.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"gender\",\n                                                children: \"Gender *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('gender') || 'male',\n                                                onValueChange: (value)=>setValue('gender', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.gender ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select gender\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"male\",\n                                                                children: \"Male\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"female\",\n                                                                children: \"Female\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"other\",\n                                                                children: \"Other\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.gender && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.gender.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionDate\",\n                                                children: \"Admission Date *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionDate\",\n                                                type: \"date\",\n                                                ...register('admissionDate'),\n                                                className: errors.admissionDate ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionDate.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"bloodGroup\",\n                                                children: \"Blood Group\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('bloodGroup') || '',\n                                                onValueChange: (value)=>setValue('bloodGroup', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.bloodGroup ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select blood group\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A+\",\n                                                                children: \"A+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A-\",\n                                                                children: \"A-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B+\",\n                                                                children: \"B+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B-\",\n                                                                children: \"B-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB+\",\n                                                                children: \"AB+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB-\",\n                                                                children: \"AB-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O+\",\n                                                                children: \"O+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O-\",\n                                                                children: \"O-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.bloodGroup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.bloodGroup.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"nationality\",\n                                                children: \"Nationality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"nationality\",\n                                                ...register('nationality'),\n                                                className: errors.nationality ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Indian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.nationality && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.nationality.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"religion\",\n                                                children: \"Religion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"religion\",\n                                                ...register('religion'),\n                                                className: errors.religion ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.religion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.religion.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"address\",\n                                        children: \"Address (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        id: \"address\",\n                                        ...register('address'),\n                                        className: errors.address ? 'border-red-500' : ''\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600\",\n                                        children: errors.address.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactName\",\n                                                children: \"Emergency Contact Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactName\",\n                                                ...register('emergencyContactName'),\n                                                className: errors.emergencyContactName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactPhone\",\n                                                children: \"Emergency Contact Phone\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactPhone\",\n                                                ...register('emergencyContactPhone'),\n                                                className: errors.emergencyContactPhone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactPhone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactPhone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactRelationship\",\n                                                children: \"Relationship\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactRelationship\",\n                                                ...register('emergencyContactRelationship'),\n                                                className: errors.emergencyContactRelationship ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Father, Mother, Guardian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactRelationship && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactRelationship.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"medicalConditions\",\n                                                children: \"Medical Conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"medicalConditions\",\n                                                ...register('medicalConditions'),\n                                                className: errors.medicalConditions ? 'border-red-500' : '',\n                                                placeholder: \"Any medical conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.medicalConditions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.medicalConditions.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"allergies\",\n                                                children: \"Allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"allergies\",\n                                                ...register('allergies'),\n                                                className: errors.allergies ? 'border-red-500' : '',\n                                                placeholder: \"Any known allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.allergies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.allergies.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 11\n                            }, this),\n                            !isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 border-t pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Password Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                id: \"generatePassword\",\n                                                checked: watch('generatePassword'),\n                                                onCheckedChange: (checked)=>setValue('generatePassword', !!checked)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"generatePassword\",\n                                                className: \"text-sm\",\n                                                children: \"Generate password automatically (recommended)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 15\n                                    }, this),\n                                    !watch('generatePassword') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"password\",\n                                                children: \"Custom Password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"password\",\n                                                type: \"password\",\n                                                ...register('password'),\n                                                className: errors.password ? 'border-red-500' : '',\n                                                placeholder: \"Enter custom password (min 8 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 19\n                                            }, this),\n                                            errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.password.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"profile_picture\",\n                                        children: \"Profile Picture (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__.FileUpload, {\n                                        accept: \"image/*\",\n                                        maxFiles: 1,\n                                        maxSize: 5 * 1024 * 1024,\n                                        autoUpload: false,\n                                        onFileSelect: (files)=>{\n                                            // Handle file selection for future implementation\n                                            console.log('Selected files:', files);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Upload a profile picture for the student (max 5MB)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: ()=>setOpen(false),\n                                        disabled: isSubmitting,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isEditMode ? 'Updating...' : 'Adding...'\n                                            ]\n                                        }, void 0, true) : isEditMode ? 'Update Student' : 'Add Student'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 742,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n        lineNumber: 333,\n        columnNumber: 5\n    }, this);\n}\n_s(AddStudentModal, \"/v7JLUIS8G/it21M1VUDKZENywk=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm\n    ];\n});\n_c = AddStudentModal;\nvar _c;\n$RefreshReg$(_c, \"AddStudentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/modals/add-student-modal.tsx\n"));

/***/ })

});